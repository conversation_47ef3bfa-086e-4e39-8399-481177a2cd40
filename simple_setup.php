<?php
/**
 * إعداد مبسط لقاعدة البيانات
 */

// إعدادات الاتصال
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'charity_gaza';

echo "<h1>إعداد قاعدة البيانات</h1>";

try {
    // الاتصال بـ MySQL
    $conn = new mysqli($host, $username, $password);
    
    if ($conn->connect_error) {
        die("فشل الاتصال: " . $conn->connect_error);
    }
    
    echo "<p>✅ تم الاتصال بـ MySQL</p>";
    
    // إنشاء قاعدة البيانات
    $sql = "CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($conn->query($sql) === TRUE) {
        echo "<p>✅ تم إنشاء قاعدة البيانات</p>";
    }
    
    // اختيار قاعدة البيانات
    $conn->select_db($database);
    
    // إنشاء جدول العدادات
    $sql = "CREATE TABLE IF NOT EXISTS counters (
        id INT AUTO_INCREMENT PRIMARY KEY,
        meals_count INT DEFAULT 15420,
        families_count INT DEFAULT 8750,
        children_count INT DEFAULT 12300,
        donors_count INT DEFAULT 2840
    )";
    $conn->query($sql);
    
    // إنشاء جدول المتبرعين
    $sql = "CREATE TABLE IF NOT EXISTS donors (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql);
    
    // إنشاء جدول التبرعات
    $sql = "CREATE TABLE IF NOT EXISTS donations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        donor_id INT,
        donor_name VARCHAR(255) NOT NULL,
        donor_email VARCHAR(255) NOT NULL,
        package_type VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        txid VARCHAR(255),
        status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql);
    
    // إنشاء جدول الشهادات
    $sql = "CREATE TABLE IF NOT EXISTS testimonials (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        role VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql);
    
    // إنشاء جدول الرسائل
    $sql = "CREATE TABLE IF NOT EXISTS contacts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(255),
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql);
    
    // إنشاء جدول المشرفين
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        role ENUM('admin', 'moderator') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql);
    
    echo "<p>✅ تم إنشاء جميع الجداول</p>";
    
    // إدراج البيانات الافتراضية
    $conn->query("INSERT IGNORE INTO counters (id, meals_count, families_count, children_count, donors_count) VALUES (1, 15420, 8750, 12300, 2840)");
    
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $conn->query("INSERT IGNORE INTO admin_users (username, password_hash, full_name, email) VALUES ('admin', '$password_hash', 'مدير النظام', '<EMAIL>')");
    
    $conn->query("INSERT IGNORE INTO testimonials (name, role, message, display_order) VALUES 
        ('أحمد محمد', 'أب لثلاثة أطفال', 'بفضل تبرعاتكم استطعنا إطعام أطفالنا لأسبوع كامل. جزاكم الله خيراً', 1),
        ('فاطمة أحمد', 'أم لخمسة أطفال', 'الوجبات الساخنة التي وصلتنا أنقذت حياة أطفالي من الجوع. شكراً لكل متبرع', 2)");
    
    echo "<p>✅ تم إدراج البيانات الافتراضية</p>";
    
    echo "<h2 style='color: green;'>🎉 تم الإعداد بنجاح!</h2>";
    echo "<p><a href='index.php'>اذهب للموقع الرئيسي</a></p>";
    echo "<p><a href='admin/'>اذهب للوحة الإدارة</a> (admin / admin123)</p>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من تشغيل XAMPP وأن MySQL يعمل</p>";
}
?>
