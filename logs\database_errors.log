2025-06-03 11:23:14 - Insert query error: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`charity_gaza`.`activity_logs`, CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL) | SQL: INSERT INTO activity_logs (admin_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?) | Params: [1,"\u062a\u062d\u062f\u064a\u062b \u0627\u0644\u0639\u062f\u0627\u062f\u0627\u062a \u0648\u0627\u0644\u0623\u0647\u062f\u0627\u0641",null,null,null,null,"::1","Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"] | IP: ::1 | User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
