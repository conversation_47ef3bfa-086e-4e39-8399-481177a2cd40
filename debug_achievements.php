<?php
/**
 * Debug script for achievements table
 * This script checks the achievements table structure and data
 */

define('CHARITY_GAZA', true);
require_once 'includes/config.php';
require_once 'includes/db.php';

echo "=== Debug Achievements Table ===\n";
echo "Database: " . DB_NAME . "\n";
echo "Host: " . DB_HOST . "\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 50) . "\n\n";

try {
    // Test database connection first
    $db = getDB();
    echo "✓ Database connection successful\n\n";
    
    // Check if achievements table exists
    $result = selectOne("SHOW TABLES LIKE 'achievements'");
    if ($result) {
        echo "✓ Achievements table exists\n";
        
        // Get table structure
        $structure = selectQuery("DESCRIBE achievements");
        echo "\n" . str_repeat("-", 30) . "\n";
        echo "TABLE STRUCTURE:\n";
        echo str_repeat("-", 30) . "\n";
        printf("%-20s %-20s %-10s %-15s %-10s %-10s\n", 'Field', 'Type', 'Null', 'Key', 'Default', 'Extra');
        echo str_repeat("-", 90) . "\n";
        foreach ($structure as $column) {
            printf("%-20s %-20s %-10s %-15s %-10s %-10s\n", 
                $column['Field'], 
                $column['Type'], 
                $column['Null'], 
                $column['Key'], 
                $column['Default'] ?: 'NULL', 
                $column['Extra']
            );
        }
        
        // Get row count
        $count_result = selectOne("SELECT COUNT(*) as total FROM achievements");
        $total_rows = $count_result['total'];
        echo "\n✓ Total records: " . $total_rows . "\n";
        
        if ($total_rows > 0) {
            // Get sample data
            $achievements = selectQuery("SELECT id, title, description, stats, achievement_date, is_active, created_at FROM achievements ORDER BY created_at DESC LIMIT 5");
            echo "\n" . str_repeat("-", 30) . "\n";
            echo "SAMPLE DATA (Latest 5 records):\n";
            echo str_repeat("-", 30) . "\n";
            
            foreach ($achievements as $i => $achievement) {
                echo "Record " . ($i + 1) . ":\n";
                echo "  ID: " . $achievement['id'] . "\n";
                echo "  Title: " . htmlspecialchars($achievement['title']) . "\n";
                echo "  Description: " . htmlspecialchars(substr($achievement['description'], 0, 100)) . (strlen($achievement['description']) > 100 ? '...' : '') . "\n";
                echo "  Stats: " . ($achievement['stats'] ? htmlspecialchars($achievement['stats']) : 'N/A') . "\n";
                echo "  Achievement Date: " . ($achievement['achievement_date'] ?: 'N/A') . "\n";
                echo "  Status: " . ($achievement['is_active'] ? 'Active' : 'Inactive') . "\n";
                echo "  Created: " . $achievement['created_at'] . "\n";
                echo "\n";
            }
            
            // Get status distribution
            $status_stats = selectQuery("SELECT is_active, COUNT(*) as count FROM achievements GROUP BY is_active");
            echo str_repeat("-", 30) . "\n";
            echo "STATUS DISTRIBUTION:\n";
            echo str_repeat("-", 30) . "\n";
            foreach ($status_stats as $stat) {
                $status = $stat['is_active'] ? 'Active' : 'Inactive';
                echo "  " . $status . ": " . $stat['count'] . " records\n";
            }
            
        } else {
            echo "\n⚠ Table is empty - no records found\n";
        }
        
    } else {
        echo "✗ Achievements table does NOT exist\n\n";
        
        echo str_repeat("-", 30) . "\n";
        echo "AVAILABLE TABLES:\n";
        echo str_repeat("-", 30) . "\n";
        $tables = selectQuery("SHOW TABLES");
        foreach ($tables as $table) {
            echo "- " . array_values($table)[0] . "\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n";
        echo "SUGGESTED SOLUTION:\n";
        echo str_repeat("-", 50) . "\n";
        echo "The achievements table needs to be created. Here's the SQL:\n\n";
        
        $create_sql = "CREATE TABLE achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    stats VARCHAR(255),
    image_url VARCHAR(500),
    achievement_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_date (achievement_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        echo $create_sql . "\n\n";
        echo "You can run this SQL in your database to create the table.\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error occurred: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Debug completed at: " . date('Y-m-d H:i:s') . "\n";
?>
