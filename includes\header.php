<?php
/**
 * ملف الهيدر المشترك
 */

// منع الوصول المباشر
if (!defined('CHARITY_GAZA')) {
    die('Access denied');
}

// الحصول على معلومات اللغة الحالية
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();
?>
<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo __('site_description'); ?>">
    <meta name="keywords" content="Gaza, donation, charity, aid, Palestine">
    <meta name="author" content="<?php echo __('site_name'); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo __('site_name'); ?>">
    <meta property="og:description" content="<?php echo __('hero_description'); ?>">
    <meta property="og:image" content="<?php echo ASSETS_URL; ?>/images/og-image.jpg">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo __('site_name'); ?>">
    <meta name="twitter:description" content="<?php echo __('hero_description'); ?>">
    <meta name="twitter:image" content="<?php echo ASSETS_URL; ?>/images/og-image.jpg">

    <title><?php echo isset($page_title) ? $page_title . ' - ' . __('site_name') : __('site_name'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <!-- CSS Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/multilang.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($is_rtl): ?>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <?php else: ?>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700;900&display=swap" rel="stylesheet">
    <?php endif; ?>

    <!-- Custom Styles -->
    <style>
        html {
            direction: <?php echo $text_direction; ?>;
        }

        body {
            <?php if ($is_rtl): ?>
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            <?php elseif ($current_lang === 'zh'): ?>
            font-family: 'Noto Sans SC', sans-serif;
            direction: ltr;
            text-align: left;
            <?php elseif ($current_lang === 'ja'): ?>
            font-family: 'Noto Sans JP', sans-serif;
            direction: ltr;
            text-align: left;
            <?php else: ?>
            font-family: 'Inter', sans-serif;
            direction: ltr;
            text-align: left;
            <?php endif; ?>
            background-color: #111;
            color: #f0f0f0;
        }

        /* فرض الاتجاه على جميع العناصر */
        * {
            direction: inherit;
        }

        /* تخصيص للعناصر الرئيسية */
        .container, .hero-bg, section, header, footer, nav {
            direction: <?php echo $text_direction; ?>;
            <?php if ($is_rtl): ?>
            text-align: right;
            <?php else: ?>
            text-align: left;
            <?php endif; ?>
        }
        
        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), 
                        url('https://images.unsplash.com/photo-1632495288245-81b9d20c0cdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
        
        .counter-box {
            transition: all 0.3s ease;
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #e63946;
        }
        
        .counter-box:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(230, 57, 70, 0.5);
        }
        
        .donate-btn {
            transition: all 0.3s ease;
            background-color: #e63946;
        }
        
        .donate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(230, 57, 70, 0.4);
            background-color: #c1121f;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .crying-icon {
            animation: cry 3s ease-in-out infinite;
        }
        
        @keyframes cry {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .floating {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .tragic-image {
            filter: grayscale(30%) brightness(0.8);
            transition: all 0.3s ease;
        }
        
        .tragic-image:hover {
            filter: grayscale(0%) brightness(1);
        }
        
        .tragic-section {
            margin-bottom: 4rem;
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .kitchen-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
                        url('https://images.unsplash.com/photo-1632495288245-81b9d20c0cdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            background-color: #1f2937;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            padding: 10px 15px;
            cursor: pointer;
        }
        
        .close:hover {
            color: #e63946;
        }
        
        .progress-bar {
            background-color: #374151;
            border-radius: 10px;
            overflow: hidden;
            height: 20px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #e63946, #dc2626);
            height: 100%;
            transition: width 2s ease-in-out;
            border-radius: 10px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background-color: #10b981;
        }
        
        .notification.error {
            background-color: #ef4444;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    
    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-gray-800 p-6 rounded-lg text-center">
            <div class="loading mx-auto mb-4"></div>
            <p class="text-white"><?php echo __('processing'); ?></p>
        </div>
    </div>





    <!-- Notification Container -->
    <div id="notificationContainer"></div>
