<?php
/**
 * صفحة إدارة التبرعات
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'verify_txid':
                $donation_id = intval($_POST['donation_id']);
                $donation = selectOne("SELECT * FROM donations WHERE id = ?", [$donation_id]);
                
                if ($donation && !empty($donation['txid'])) {
                    $verification = verifyTronTransaction($donation['txid'], $donation['amount']);
                    
                    if ($verification['success']) {
                        updateQuery(
                            "UPDATE donations SET status = 'confirmed', notes = ? WHERE id = ?",
                            ['تم التحقق من المعاملة تلقائياً', $donation_id]
                        );
                        
                        // إرسال تأكيد للمتبرع
                        sendDonationConfirmation($donation['donor_email'], $donation['donor_name'], $donation['amount'], $donation['package_type'], $donation['txid']);
                        
                        logAdminActivity('تأكيد تبرع', 'donations', $donation_id);
                        $message = 'تم تأكيد التبرع بنجاح';
                        $message_type = 'success';
                    } else {
                        updateQuery(
                            "UPDATE donations SET verification_attempts = verification_attempts + 1, notes = ? WHERE id = ?",
                            [$verification['message'], $donation_id]
                        );
                        $message = 'فشل في التحقق: ' . $verification['message'];
                        $message_type = 'error';
                    }
                }
                break;
                
            case 'update_status':
                $donation_id = intval($_POST['donation_id']);
                $status = $_POST['status'];
                $notes = sanitizeInput($_POST['notes'] ?? '');
                
                $old_donation = selectOne("SELECT * FROM donations WHERE id = ?", [$donation_id]);
                
                updateQuery(
                    "UPDATE donations SET status = ?, notes = ? WHERE id = ?",
                    [$status, $notes, $donation_id]
                );
                
                logAdminActivity('تحديث حالة تبرع', 'donations', $donation_id, $old_donation, ['status' => $status, 'notes' => $notes]);
                $message = 'تم تحديث حالة التبرع بنجاح';
                $message_type = 'success';
                break;
                
            case 'delete_donation':
                $donation_id = intval($_POST['donation_id']);
                $donation = selectOne("SELECT * FROM donations WHERE id = ?", [$donation_id]);
                
                deleteQuery("DELETE FROM donations WHERE id = ?", [$donation_id]);
                
                logAdminActivity('حذف تبرع', 'donations', $donation_id, $donation);
                $message = 'تم حذف التبرع بنجاح';
                $message_type = 'success';
                break;
        }
    }
}

// فلترة البيانات
$status_filter = $_GET['status'] ?? 'all';
$search = sanitizeInput($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(donor_name LIKE ? OR donor_email LIKE ? OR txid LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// جلب التبرعات
$donations = selectQuery(
    "SELECT d.*, DATE_FORMAT(d.created_at, '%Y-%m-%d %H:%i') as formatted_date 
     FROM donations d 
     {$where_clause} 
     ORDER BY d.created_at DESC 
     LIMIT {$per_page} OFFSET {$offset}",
    $params
);

// عدد التبرعات الإجمالي
$total_donations = selectOne(
    "SELECT COUNT(*) as count FROM donations d {$where_clause}",
    $params
)['count'] ?? 0;

$total_pages = ceil($total_donations / $per_page);

// إحصائيات سريعة
$stats = [
    'total' => selectOne("SELECT COUNT(*) as count FROM donations")['count'] ?? 0,
    'confirmed' => selectOne("SELECT COUNT(*) as count FROM donations WHERE status = 'confirmed'")['count'] ?? 0,
    'pending' => selectOne("SELECT COUNT(*) as count FROM donations WHERE status = 'pending'")['count'] ?? 0,
    'rejected' => selectOne("SELECT COUNT(*) as count FROM donations WHERE status = 'rejected'")['count'] ?? 0,
    'total_amount' => selectOne("SELECT SUM(amount) as total FROM donations WHERE status = 'confirmed'")['total'] ?? 0
];

$page_title = 'إدارة التبرعات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid #334155;
        }
        
        .table-container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
        }
        
        .nav-link {
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background-color: rgba(230, 57, 70, 0.1);
            border-right: 3px solid #e63946;
        }
        
        .nav-link.active {
            background-color: rgba(230, 57, 70, 0.2);
            border-right: 3px solid #e63946;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            background-color: #1e293b;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
        }
    </style>
</head>
<body class="bg-slate-900">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">لوحة الإدارة</h1>
                        <p class="text-sm text-gray-400"><?php echo SITE_NAME; ?></p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="index.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="mr-3">الرئيسية</span>
                    </a>
                    <a href="donations.php" class="nav-link active flex items-center px-4 py-3 text-white rounded-lg">
                        <i class="fas fa-hand-holding-heart w-5"></i>
                        <span class="mr-3">التبرعات</span>
                        <?php if ($stats['pending'] > 0): ?>
                            <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full mr-auto">
                                <?php echo $stats['pending']; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="contacts.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="mr-3">الرسائل</span>
                    </a>
                    <a href="testimonials.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-quote-right w-5"></i>
                        <span class="mr-3">الشهادات</span>
                    </a>
                    <a href="achievements.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-trophy w-5"></i>
                        <span class="mr-3">الإنجازات</span>
                    </a>
                    <a href="counters.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span class="mr-3">العدادات</span>
                    </a>
                    <a href="settings.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-cog w-5"></i>
                        <span class="mr-3">الإعدادات</span>
                    </a>
                </nav>
            </div>
            
            <!-- User Info -->
            <div class="absolute bottom-0 w-full p-6 border-t border-gray-700">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></p>
                        <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['admin_role']); ?></p>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-400" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-slate-800 border-b border-slate-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-white">إدارة التبرعات</h1>
                        <p class="text-gray-400">عرض وإدارة جميع التبرعات الواردة</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="../index.php" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>عرض الموقع
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <main class="p-6">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-600' : 'bg-red-600'; ?> text-white">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-white"><?php echo number_format($stats['total']); ?></p>
                            <p class="text-gray-400 text-sm">إجمالي التبرعات</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-400"><?php echo number_format($stats['confirmed']); ?></p>
                            <p class="text-gray-400 text-sm">مؤكدة</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-yellow-400"><?php echo number_format($stats['pending']); ?></p>
                            <p class="text-gray-400 text-sm">معلقة</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-red-400"><?php echo number_format($stats['rejected']); ?></p>
                            <p class="text-gray-400 text-sm">مرفوضة</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-blue-400">$<?php echo number_format($stats['total_amount'], 2); ?></p>
                            <p class="text-gray-400 text-sm">المبلغ الإجمالي</p>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="table-container rounded-xl p-6 mb-6">
                    <form method="GET" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input 
                                type="text" 
                                name="search" 
                                placeholder="البحث بالاسم، البريد، أو TXID..." 
                                value="<?php echo htmlspecialchars($search); ?>"
                                class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500"
                            >
                        </div>
                        <div>
                            <select name="status" class="px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                <option value="confirmed" <?php echo $status_filter === 'confirmed' ? 'selected' : ''; ?>>مؤكدة</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-search mr-2"></i>بحث
                        </button>
                        <a href="donations.php" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-times mr-2"></i>إعادة تعيين
                        </a>
                    </form>
                </div>
                
                <!-- Donations Table -->
                <div class="table-container rounded-xl overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-slate-800">
                                <tr>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">المتبرع</th>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">نوع التبرع</th>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">المبلغ</th>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">TXID</th>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">الحالة</th>
                                    <th class="text-right py-4 px-6 text-gray-300 font-medium">التاريخ</th>
                                    <th class="text-center py-4 px-6 text-gray-300 font-medium">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($donations)): ?>
                                    <?php foreach ($donations as $donation): ?>
                                        <tr class="border-b border-slate-700 hover:bg-slate-800">
                                            <td class="py-4 px-6">
                                                <div>
                                                    <p class="text-white font-medium"><?php echo htmlspecialchars($donation['donor_name']); ?></p>
                                                    <p class="text-gray-400 text-sm"><?php echo htmlspecialchars($donation['donor_email']); ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-6 text-white"><?php echo htmlspecialchars($donation['package_type']); ?></td>
                                            <td class="py-4 px-6 text-white font-bold">$<?php echo number_format($donation['amount'], 2); ?></td>
                                            <td class="py-4 px-6">
                                                <?php if (!empty($donation['txid'])): ?>
                                                    <span class="text-green-400 font-mono text-xs"><?php echo htmlspecialchars(substr($donation['txid'], 0, 20)) . '...'; ?></span>
                                                <?php else: ?>
                                                    <span class="text-gray-500">غير متوفر</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="py-4 px-6">
                                                <?php if ($donation['status'] === 'confirmed'): ?>
                                                    <span class="bg-green-600 text-white px-3 py-1 rounded-full text-xs">مؤكد</span>
                                                <?php elseif ($donation['status'] === 'pending'): ?>
                                                    <span class="bg-yellow-600 text-white px-3 py-1 rounded-full text-xs">معلق</span>
                                                <?php else: ?>
                                                    <span class="bg-red-600 text-white px-3 py-1 rounded-full text-xs">مرفوض</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="py-4 px-6 text-gray-400 text-sm"><?php echo $donation['formatted_date']; ?></td>
                                            <td class="py-4 px-6 text-center">
                                                <div class="flex justify-center space-x-2 space-x-reverse">
                                                    <?php if ($donation['status'] === 'pending' && !empty($donation['txid'])): ?>
                                                        <form method="POST" class="inline">
                                                            <input type="hidden" name="action" value="verify_txid">
                                                            <input type="hidden" name="donation_id" value="<?php echo $donation['id']; ?>">
                                                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs" title="التحقق من TXID">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <button onclick="editDonation(<?php echo htmlspecialchars(json_encode($donation)); ?>)" class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    
                                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا التبرع؟')">
                                                        <input type="hidden" name="action" value="delete_donation">
                                                        <input type="hidden" name="donation_id" value="<?php echo $donation['id']; ?>">
                                                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="py-12 text-center text-gray-400">
                                            <i class="fas fa-inbox text-4xl mb-4"></i>
                                            <p>لا توجد تبرعات مطابقة للبحث</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="bg-slate-800 px-6 py-4 border-t border-slate-700">
                            <div class="flex items-center justify-between">
                                <div class="text-gray-400 text-sm">
                                    عرض <?php echo ($offset + 1); ?> إلى <?php echo min($offset + $per_page, $total_donations); ?> من أصل <?php echo $total_donations; ?> تبرع
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" class="bg-slate-700 hover:bg-slate-600 text-white px-3 py-2 rounded">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" 
                                           class="<?php echo $i === $page ? 'bg-red-600' : 'bg-slate-700 hover:bg-slate-600'; ?> text-white px-3 py-2 rounded">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" class="bg-slate-700 hover:bg-slate-600 text-white px-3 py-2 rounded">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-white">تعديل التبرع</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="donation_id" id="editDonationId">
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">الحالة</label>
                    <select name="status" id="editStatus" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white">
                        <option value="pending">معلق</option>
                        <option value="confirmed">مؤكد</option>
                        <option value="rejected">مرفوض</option>
                    </select>
                </div>
                
                <div class="mb-6">
                    <label class="block text-gray-300 mb-2">ملاحظات</label>
                    <textarea name="notes" id="editNotes" rows="3" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white resize-none"></textarea>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        حفظ التغييرات
                    </button>
                    <button type="button" onclick="closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function editDonation(donation) {
            document.getElementById('editDonationId').value = donation.id;
            document.getElementById('editStatus').value = donation.status;
            document.getElementById('editNotes').value = donation.notes || '';
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
