<?php
/**
 * صفحة إدارة الإنجازات
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_achievement':
                $title = sanitizeInput($_POST['title']);
                $description = sanitizeInput($_POST['description']);
                $stats = sanitizeInput($_POST['stats'] ?? '');
                $achievement_date = sanitizeInput($_POST['achievement_date'] ?? date('Y-m-d'));
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                // معالجة رفع الصورة
                $image_url = '';
                if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                    $image_url = handleImageUpload($_FILES['image_file'], 'achievements');
                } elseif (!empty($_POST['image_url'])) {
                    $image_url = sanitizeInput($_POST['image_url']);
                }

                if (!empty($title) && !empty($description)) {
                    $achievementId = insertQuery(
                        "INSERT INTO achievements (title, description, stats, image_url, achievement_date, is_active)
                         VALUES (?, ?, ?, ?, ?, ?)",
                        [$title, $description, $stats, $image_url, $achievement_date, $is_active]
                    );
                    
                    if ($achievementId) {
                        logAdminActivity('إضافة إنجاز', 'achievements', $achievementId);
                        $message = 'تم إضافة الإنجاز بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء إضافة الإنجاز';
                        $message_type = 'error';
                    }
                } else {
                    $message = 'العنوان والوصف مطلوبان';
                    $message_type = 'error';
                }
                break;
                
            case 'edit_achievement':
                $id = intval($_POST['id']);
                $title = sanitizeInput($_POST['title']);
                $description = sanitizeInput($_POST['description']);
                $stats = sanitizeInput($_POST['stats'] ?? '');
                $achievement_date = sanitizeInput($_POST['achievement_date'] ?? date('Y-m-d'));
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                if (!empty($title) && !empty($description)) {
                    $old_achievement = selectOne("SELECT * FROM achievements WHERE id = ?", [$id]);

                    // معالجة رفع الصورة
                    $image_url = $old_achievement['image_url'] ?? '';
                    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                        $new_image = handleImageUpload($_FILES['image_file'], 'achievements');
                        if ($new_image) {
                            // حذف الصورة القديمة إذا كانت موجودة
                            if (!empty($image_url) && file_exists("../assets/uploads/" . basename($image_url))) {
                                unlink("../assets/uploads/" . basename($image_url));
                            }
                            $image_url = $new_image;
                        }
                    } elseif (!empty($_POST['image_url'])) {
                        $image_url = sanitizeInput($_POST['image_url']);
                    }

                    $updated = updateQuery(
                        "UPDATE achievements SET title = ?, description = ?, stats = ?, image_url = ?, achievement_date = ?, is_active = ? WHERE id = ?",
                        [$title, $description, $stats, $image_url, $achievement_date, $is_active, $id]
                    );
                    
                    if ($updated) {
                        logAdminActivity('تعديل إنجاز', 'achievements', $id, $old_achievement, $_POST);
                        $message = 'تم تحديث الإنجاز بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء تحديث الإنجاز';
                        $message_type = 'error';
                    }
                } else {
                    $message = 'العنوان والوصف مطلوبان';
                    $message_type = 'error';
                }
                break;
                
            case 'delete_achievement':
                $id = intval($_POST['id']);
                $achievement = selectOne("SELECT * FROM achievements WHERE id = ?", [$id]);
                
                if ($achievement) {
                    $deleted = deleteQuery("DELETE FROM achievements WHERE id = ?", [$id]);
                    
                    if ($deleted) {
                        logAdminActivity('حذف إنجاز', 'achievements', $id, $achievement);
                        $message = 'تم حذف الإنجاز بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء حذف الإنجاز';
                        $message_type = 'error';
                    }
                } else {
                    $message = 'الإنجاز غير موجود';
                    $message_type = 'error';
                }
                break;
                
            case 'toggle_status':
                $id = intval($_POST['id']);
                $achievement = selectOne("SELECT * FROM achievements WHERE id = ?", [$id]);
                
                if ($achievement) {
                    $new_status = $achievement['is_active'] ? 0 : 1;
                    $updated = updateQuery("UPDATE achievements SET is_active = ? WHERE id = ?", [$new_status, $id]);
                    
                    if ($updated) {
                        logAdminActivity('تغيير حالة إنجاز', 'achievements', $id, $achievement, ['is_active' => $new_status]);
                        $message = 'تم تغيير حالة الإنجاز بنجاح';
                        $message_type = 'success';
                    }
                }
                break;
        }
    }
}

// جلب الإنجازات مرتبة حسب تاريخ الإنجاز (الأحدث أولاً)
$achievements = selectQuery("SELECT * FROM achievements ORDER BY achievement_date DESC, created_at DESC") ?: [];

$page_title = 'إدارة الإنجازات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - لوحة الإدارة</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/favicon.png">
    <link rel="apple-touch-icon" href="../assets/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body { font-family: 'Tajawal', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo $page_title; ?></h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="../charity_kitchen.php" class="text-blue-600 hover:text-blue-800" target="_blank">
                        <i class="fas fa-external-link-alt mr-1"></i>عرض الصفحة
                    </a>
                    <a href="index.php" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-right mr-1"></i>العودة للوحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- رسائل التنبيه -->
        <?php if (!empty($message)): ?>
            <div class="mb-4 p-4 rounded-md <?php echo $message_type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'; ?>">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?> mr-2"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- زر إضافة إنجاز جديد -->
        <div class="mb-6">
            <button onclick="openAddModal()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i>إضافة إنجاز جديد
            </button>
        </div>

        <!-- جدول الإنجازات -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">قائمة الإنجازات</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">إدارة إنجازات ومشاريع التكية الخيرية</p>
            </div>
            
            <?php if (!empty($achievements)): ?>
                <div class="border-t border-gray-200">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإحصائيات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنجاز</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($achievements as $achievement): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($achievement['title']); ?></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 max-w-xs truncate"><?php echo htmlspecialchars($achievement['description']); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo htmlspecialchars($achievement['stats'] ?: 'غير محدد'); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo $achievement['achievement_date'] ? date('Y/m/d', strtotime($achievement['achievement_date'])) : 'غير محدد'; ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $achievement['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                <?php echo $achievement['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo date('Y/m/d H:i', strtotime($achievement['created_at'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <button onclick="editAchievement(<?php echo htmlspecialchars(json_encode($achievement)); ?>)" class="text-blue-600 hover:text-blue-900">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="id" value="<?php echo $achievement['id']; ?>">
                                                    <button type="submit" class="text-yellow-600 hover:text-yellow-900" title="تغيير الحالة">
                                                        <i class="fas fa-toggle-<?php echo $achievement['is_active'] ? 'on' : 'off'; ?>"></i>
                                                    </button>
                                                </form>
                                                <button onclick="deleteAchievement(<?php echo $achievement['id']; ?>, '<?php echo htmlspecialchars($achievement['title']); ?>')" class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-trophy text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إنجازات</h3>
                    <p class="text-gray-500 mb-4">ابدأ بإضافة أول إنجاز للتكية الخيرية</p>
                    <button onclick="openAddModal()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-plus mr-2"></i>إضافة إنجاز جديد
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal إضافة/تعديل إنجاز -->
    <div id="achievementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">إضافة إنجاز جديد</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <form id="achievementForm" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" id="formAction" value="add_achievement">
                    <input type="hidden" name="id" id="achievementId">

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الإنجاز *</label>
                            <input type="text" name="title" id="achievementTitle" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وصف الإنجاز *</label>
                            <textarea name="description" id="achievementDescription" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الإحصائيات</label>
                            <input type="text" name="stats" id="achievementStats" placeholder="مثال: 1000 مستفيد - 50 عائلة" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنجاز</label>
                            <input type="date" name="achievement_date" id="achievementDate" value="<?php echo date('Y-m-d'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الصورة</label>
                            <div class="space-y-2">
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">رفع صورة من الجهاز</label>
                                    <input type="file" name="image_file" id="achievementImageFile" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="text-center text-gray-400 text-sm">أو</div>
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">رابط الصورة</label>
                                    <input type="url" name="image_url" id="achievementImageUrl" placeholder="https://example.com/image.jpg" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="achievementActive" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="achievementActive" class="mr-2 block text-sm text-gray-900">نشط (يظهر في الموقع)</label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">إلغاء</button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة إنجاز جديد';
            document.getElementById('formAction').value = 'add_achievement';
            document.getElementById('achievementForm').reset();
            document.getElementById('achievementActive').checked = true;
            document.getElementById('achievementModal').classList.remove('hidden');
        }
        
        function editAchievement(achievement) {
            document.getElementById('modalTitle').textContent = 'تعديل الإنجاز';
            document.getElementById('formAction').value = 'edit_achievement';
            document.getElementById('achievementId').value = achievement.id;
            document.getElementById('achievementTitle').value = achievement.title;
            document.getElementById('achievementDescription').value = achievement.description;
            document.getElementById('achievementStats').value = achievement.stats || '';
            document.getElementById('achievementDate').value = achievement.achievement_date || '';
            document.getElementById('achievementImageUrl').value = achievement.image_url || '';
            document.getElementById('achievementActive').checked = achievement.is_active == 1;
            document.getElementById('achievementModal').classList.remove('hidden');
        }
        
        function closeModal() {
            document.getElementById('achievementModal').classList.add('hidden');
        }
        
        function deleteAchievement(id, title) {
            if (confirm('هل أنت متأكد من حذف الإنجاز "' + title + '"؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_achievement">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // إغلاق المودال عند النقر خارجه
        document.getElementById('achievementModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
