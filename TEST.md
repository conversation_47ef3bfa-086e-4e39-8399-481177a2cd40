# دليل اختبار موقع يداً بيد لأجل غزة

## ✅ قائمة الاختبارات المطلوبة

### 1. اختبار الصفحة الرئيسية
- [ ] تحميل الصفحة الرئيسية بنجاح
- [ ] عرض العدادات المتحركة
- [ ] عمل أزرار التبرع
- [ ] عرض الشهادات
- [ ] عمل نموذج التواصل

### 2. اختبار نظام التبرعات
- [ ] فتح نافذة التبرع عند الضغط على الأزرار
- [ ] عرض عنوان محفظة TRON
- [ ] إدخال بيانات التبرع
- [ ] إرسال طلب التبرع
- [ ] حفظ البيانات في قاعدة البيانات

### 3. اختبار لوحة الإدارة
- [ ] تسجيل الدخول بالبيانات الافتراضية
- [ ] عرض الإحصائيات في الرئيسية
- [ ] إدارة التبرعات (عرض، تأكيد، حذف)
- [ ] إدارة الرسائل (قراءة، ملاحظات، حذف)
- [ ] إدارة الشهادات (إضافة، تعديل، حذف)
- [ ] تحديث العدادات والأهداف

### 4. اختبار التحقق من TXID
- [ ] إدخال TXID صحيح
- [ ] التحقق من المعاملة عبر TRON API
- [ ] تحديث حالة التبرع
- [ ] إرسال تأكيد بالبريد

## 🔧 خطوات الاختبار التفصيلية

### الخطوة 1: إعداد البيئة
```bash
# 1. تأكد من تشغيل XAMPP
# 2. تأكد من إنشاء قاعدة البيانات
# 3. تأكد من استيراد ملف SQL
```

### الخطوة 2: اختبار الصفحة الرئيسية
1. افتح `http://localhost/charity_gaza`
2. تحقق من تحميل الصفحة بدون أخطاء
3. انتظر تحميل العدادات المتحركة
4. اضغط على زر "تبرع الآن" وتأكد من فتح النافذة
5. املأ نموذج التواصل وأرسله

### الخطوة 3: اختبار التبرعات
1. اضغط على أي زر تبرع
2. تأكد من ظهور النافذة المنبثقة
3. املأ البيانات:
   - الاسم: أحمد محمد
   - البريد: <EMAIL>
   - المبلغ: 10
   - TXID: (اختياري)
4. اضغط "إرسال طلب التبرع"
5. تأكد من ظهور رسالة النجاح

### الخطوة 4: اختبار لوحة الإدارة
1. اذهب إلى `http://localhost/charity_gaza/admin`
2. سجل دخول بـ:
   - اسم المستخدم: admin
   - كلمة المرور: admin123
3. تصفح جميع الأقسام
4. جرب تأكيد التبرع الذي أدخلته
5. أضف شهادة جديدة
6. حدث العدادات

### الخطوة 5: اختبار الوظائف المتقدمة
1. جرب البحث في التبرعات
2. جرب الفلترة حسب الحالة
3. أضف ملاحظات لرسالة
4. غير حالة شهادة من نشط إلى غير نشط

## 🐛 استكشاف الأخطاء الشائعة

### خطأ الاتصال بقاعدة البيانات
```
Error: Database connection failed
```
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات الاتصال في `includes/config.php`
3. تأكد من إنشاء قاعدة البيانات `charity_gaza`

### خطأ في العدادات
```
Error: Cannot read property of undefined
```
**الحل:**
1. تأكد من وجود بيانات في جدول `counters`
2. أدخل البيانات الافتراضية يدوياً إذا لزم الأمر

### خطأ في الصلاحيات
```
Error: Permission denied
```
**الحل:**
1. تأكد من صلاحيات الكتابة لمجلد `logs/`
2. في Linux/Mac: `chmod 755 logs/`

### خطأ في JavaScript
```
Error: Function not defined
```
**الحل:**
1. تأكد من تحميل ملف `assets/js/main.js`
2. تحقق من وجود أخطاء في وحدة تحكم المتصفح

## 📊 نتائج الاختبار المتوقعة

### الصفحة الرئيسية
- ✅ تحميل سريع (أقل من 3 ثواني)
- ✅ عدادات متحركة تعمل بسلاسة
- ✅ تصميم متجاوب على جميع الأجهزة
- ✅ نوافذ التبرع تفتح وتغلق بشكل صحيح

### لوحة الإدارة
- ✅ تسجيل دخول آمن
- ✅ عرض البيانات بشكل صحيح
- ✅ جميع العمليات (إضافة، تعديل، حذف) تعمل
- ✅ الفلترة والبحث يعملان

### قاعدة البيانات
- ✅ جميع الجداول موجودة
- ✅ البيانات تُحفظ بشكل صحيح
- ✅ العلاقات بين الجداول سليمة
- ✅ الفهارس تعمل بكفاءة

## 🎯 معايير النجاح

### الأداء
- وقت تحميل الصفحة < 3 ثواني
- استجابة AJAX < 1 ثانية
- استخدام ذاكرة معقول

### الأمان
- حماية من SQL Injection
- حماية من XSS
- تشفير كلمات المرور
- جلسات آمنة

### سهولة الاستخدام
- واجهة بديهية
- رسائل خطأ واضحة
- تصميم متجاوب
- إمكانية الوصول

## 📝 تقرير الاختبار

بعد إكمال جميع الاختبارات، املأ هذا التقرير:

### الوظائف التي تعمل بشكل صحيح:
- [ ] الصفحة الرئيسية
- [ ] نظام التبرعات
- [ ] لوحة الإدارة
- [ ] إدارة التبرعات
- [ ] إدارة الرسائل
- [ ] إدارة الشهادات
- [ ] العدادات والإحصائيات

### المشاكل المكتشفة:
- [ ] لا توجد مشاكل
- [ ] مشاكل طفيفة (اذكرها)
- [ ] مشاكل كبيرة (اذكرها)

### التوصيات:
- [ ] المشروع جاهز للاستخدام
- [ ] يحتاج تحسينات طفيفة
- [ ] يحتاج مراجعة شاملة

---

**ملاحظة:** هذا الدليل يساعدك على اختبار جميع وظائف الموقع بشكل منهجي. تأكد من اختبار كل وظيفة قبل الانتقال للتالية.
