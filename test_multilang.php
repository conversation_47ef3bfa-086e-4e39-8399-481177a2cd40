<?php
/**
 * ملف اختبار نظام اللغات المتعددة
 * Multi-language System Test File
 */

// تعريف ثابت للأمان
define('CHARITY_GAZA', true);

// تضمين ملفات النظام
require_once 'includes/config.php';
require_once 'includes/functions.php';

// بدء الجلسة (إذا لم تكن مبدوءة بالفعل)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معلومات الصفحة
$page_title = 'اختبار نظام اللغات المتعددة';
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

// تضمين الهيدر
include 'includes/header.php';
?>

<div class="min-h-screen bg-gray-900 text-white py-8">
    <div class="container mx-auto px-4">
        
        <!-- عنوان الصفحة -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-red-500 mb-4">
                <?php echo __('site_name'); ?>
            </h1>
            <h2 class="text-2xl text-gray-300 mb-2">
                Multi-Language System Test / اختبار نظام اللغات المتعددة
            </h2>
            <p class="text-gray-400">
                Current Language: <span class="text-green-400"><?php echo $current_lang; ?></span> | 
                Direction: <span class="text-blue-400"><?php echo $text_direction; ?></span>
            </p>
        </div>

        <!-- معلومات النظام -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            
            <!-- معلومات اللغة الحالية -->
            <div class="bg-gray-800 p-6 rounded-lg border border-red-900">
                <h3 class="text-xl font-bold text-red-500 mb-4">
                    <i class="fas fa-language mr-2"></i>
                    Current Language Info
                </h3>
                <?php $lang_info = LanguageManager::getInstance()->getCurrentLanguageInfo(); ?>
                <div class="space-y-2 text-sm">
                    <p><strong>Code:</strong> <?php echo $current_lang; ?></p>
                    <p><strong>Name:</strong> <?php echo $lang_info['name'] ?? 'N/A'; ?></p>
                    <p><strong>Native:</strong> <?php echo $lang_info['native_name'] ?? 'N/A'; ?></p>
                    <p><strong>Flag:</strong> <?php echo $lang_info['flag'] ?? 'N/A'; ?></p>
                    <p><strong>Direction:</strong> <?php echo $lang_info['dir'] ?? 'N/A'; ?></p>
                </div>
            </div>

            <!-- اللغات المدعومة -->
            <div class="bg-gray-800 p-6 rounded-lg border border-red-900">
                <h3 class="text-xl font-bold text-red-500 mb-4">
                    <i class="fas fa-globe mr-2"></i>
                    Supported Languages
                </h3>
                <?php $supported = LanguageManager::getInstance()->getSupportedLanguages(); ?>
                <div class="space-y-1 text-sm max-h-40 overflow-y-auto">
                    <?php foreach ($supported as $code => $info): ?>
                        <div class="flex items-center justify-between">
                            <span><?php echo $info['flag']; ?> <?php echo $info['native_name']; ?></span>
                            <span class="text-gray-400"><?php echo $code; ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- إحصائيات النظام -->
            <div class="bg-gray-800 p-6 rounded-lg border border-red-900">
                <h3 class="text-xl font-bold text-red-500 mb-4">
                    <i class="fas fa-chart-bar mr-2"></i>
                    System Stats
                </h3>
                <div class="space-y-2 text-sm">
                    <p><strong>Total Languages:</strong> <?php echo count($supported); ?></p>
                    <p><strong>RTL Languages:</strong> 1 (Arabic)</p>
                    <p><strong>LTR Languages:</strong> <?php echo count($supported) - 1; ?></p>
                    <p><strong>Default:</strong> <?php echo DEFAULT_LANGUAGE; ?></p>
                    <p><strong>Admin:</strong> <?php echo ADMIN_LANGUAGE; ?></p>
                </div>
            </div>
        </div>

        <!-- اختبار الترجمات -->
        <div class="bg-gray-800 p-8 rounded-lg border border-red-900 mb-12">
            <h3 class="text-2xl font-bold text-red-500 mb-6">
                <i class="fas fa-text-width mr-2"></i>
                Translation Test / اختبار الترجمات
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                
                <!-- النصوص الأساسية -->
                <div>
                    <h4 class="text-lg font-bold text-white mb-4">Basic Texts</h4>
                    <div class="space-y-3">
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Site Name:</strong><br>
                            <span class="text-green-400"><?php echo __('site_name'); ?></span>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Hero Title:</strong><br>
                            <span class="text-green-400"><?php echo __('hero_title'); ?></span>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Hero Description:</strong><br>
                            <span class="text-green-400"><?php echo __('hero_description'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- التنقل -->
                <div>
                    <h4 class="text-lg font-bold text-white mb-4">Navigation</h4>
                    <div class="space-y-3">
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Home:</strong> <span class="text-blue-400"><?php echo __('nav_home'); ?></span>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>About:</strong> <span class="text-blue-400"><?php echo __('nav_about'); ?></span>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Donate:</strong> <span class="text-blue-400"><?php echo __('nav_donate'); ?></span>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>Contact:</strong> <span class="text-blue-400"><?php echo __('nav_contact'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار حزم التبرع -->
        <div class="bg-gray-800 p-8 rounded-lg border border-red-900 mb-12">
            <h3 class="text-2xl font-bold text-red-500 mb-6">
                <i class="fas fa-gift mr-2"></i>
                Donation Packages Test / اختبار حزم التبرع
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                
                <!-- وجبة ساخنة -->
                <div class="bg-gray-700 p-6 rounded-lg text-center">
                    <i class="fas fa-utensils text-3xl text-red-500 mb-3"></i>
                    <h4 class="text-lg font-bold text-white mb-2"><?php echo __('package_meal'); ?></h4>
                    <p class="text-gray-300 text-sm mb-4"><?php echo __('package_meal_desc'); ?></p>
                    <div class="text-2xl font-bold text-red-500">$5 <?php echo __('currency'); ?></div>
                </div>

                <!-- مساعدة عائلة -->
                <div class="bg-gray-700 p-6 rounded-lg text-center">
                    <i class="fas fa-home text-3xl text-red-500 mb-3"></i>
                    <h4 class="text-lg font-bold text-white mb-2"><?php echo __('package_family_aid'); ?></h4>
                    <p class="text-gray-300 text-sm mb-4"><?php echo __('package_family_aid_desc'); ?></p>
                    <div class="text-2xl font-bold text-red-500">$50 <?php echo __('currency'); ?></div>
                </div>

                <!-- مأوى آمن -->
                <div class="bg-gray-700 p-6 rounded-lg text-center">
                    <i class="fas fa-shield-alt text-3xl text-red-500 mb-3"></i>
                    <h4 class="text-lg font-bold text-white mb-2"><?php echo __('package_shelter'); ?></h4>
                    <p class="text-gray-300 text-sm mb-4"><?php echo __('package_shelter_desc'); ?></p>
                    <div class="text-2xl font-bold text-red-500">$100 <?php echo __('currency'); ?></div>
                </div>
            </div>
        </div>

        <!-- اختبار الاتجاهات -->
        <div class="bg-gray-800 p-8 rounded-lg border border-red-900 mb-12">
            <h3 class="text-2xl font-bold text-red-500 mb-6">
                <i class="fas fa-arrows-alt-h mr-2"></i>
                Direction Test / اختبار الاتجاهات
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                
                <!-- اختبار RTL -->
                <div class="bg-gray-700 p-6 rounded-lg">
                    <h4 class="text-lg font-bold text-white mb-4">RTL Test (Arabic)</h4>
                    <div class="space-y-3">
                        <div class="flex items-center <?php echo $is_rtl ? 'justify-end' : 'justify-start'; ?>">
                            <i class="fas fa-arrow-<?php echo $is_rtl ? 'left' : 'right'; ?> text-red-500 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                            <span>Direction Icon Test</span>
                        </div>
                        <div class="text-<?php echo $is_rtl ? 'right' : 'left'; ?> bg-gray-600 p-3 rounded">
                            <p>This text should be aligned to the <?php echo $is_rtl ? 'right' : 'left'; ?> in <?php echo $is_rtl ? 'RTL' : 'LTR'; ?> mode.</p>
                        </div>
                        <div class="flex items-center space-x-2 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                            <button class="bg-red-600 px-4 py-2 rounded text-white">Button 1</button>
                            <button class="bg-blue-600 px-4 py-2 rounded text-white">Button 2</button>
                        </div>
                    </div>
                </div>

                <!-- معلومات الاتجاه -->
                <div class="bg-gray-700 p-6 rounded-lg">
                    <h4 class="text-lg font-bold text-white mb-4">Direction Info</h4>
                    <div class="space-y-2 text-sm">
                        <p><strong>Current Direction:</strong> <span class="text-<?php echo $is_rtl ? 'green' : 'blue'; ?>-400"><?php echo $text_direction; ?></span></p>
                        <p><strong>Is RTL:</strong> <span class="text-<?php echo $is_rtl ? 'green' : 'red'; ?>-400"><?php echo $is_rtl ? 'Yes' : 'No'; ?></span></p>
                        <p><strong>HTML Dir:</strong> <code class="bg-gray-600 px-2 py-1 rounded"><?php echo $text_direction; ?></code></p>
                        <p><strong>CSS Classes:</strong> <code class="bg-gray-600 px-2 py-1 rounded"><?php echo $is_rtl ? 'rtl-layout' : 'ltr-layout'; ?></code></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار الوظائف -->
        <div class="bg-gray-800 p-8 rounded-lg border border-red-900 mb-12">
            <h3 class="text-2xl font-bold text-red-500 mb-6">
                <i class="fas fa-cogs mr-2"></i>
                Function Test / اختبار الوظائف
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                
                <!-- اختبار الدوال -->
                <div>
                    <h4 class="text-lg font-bold text-white mb-4">Helper Functions</h4>
                    <div class="space-y-3 text-sm">
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>getCurrentLanguage():</strong><br>
                            <code class="text-green-400"><?php echo getCurrentLanguage(); ?></code>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>getTextDirection():</strong><br>
                            <code class="text-blue-400"><?php echo getTextDirection(); ?></code>
                        </div>
                        <div class="bg-gray-700 p-3 rounded">
                            <strong>isRTL():</strong><br>
                            <code class="text-<?php echo isRTL() ? 'green' : 'red'; ?>-400"><?php echo isRTL() ? 'true' : 'false'; ?></code>
                        </div>
                    </div>
                </div>

                <!-- اختبار منتقي اللغة -->
                <div>
                    <h4 class="text-lg font-bold text-white mb-4">Language Selector</h4>
                    <div class="bg-gray-700 p-4 rounded">
                        <?php echo renderLanguageSelector('w-full'); ?>
                    </div>
                    <p class="text-gray-400 text-sm mt-2">
                        The language selector should appear above. Try changing the language to test the system.
                    </p>
                </div>
            </div>
        </div>

        <!-- روابط الاختبار -->
        <div class="bg-gray-800 p-8 rounded-lg border border-red-900">
            <h3 class="text-2xl font-bold text-red-500 mb-6">
                <i class="fas fa-link mr-2"></i>
                Test Links / روابط الاختبار
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <?php foreach ($supported as $code => $info): ?>
                    <a href="?lang=<?php echo $code; ?>" 
                       class="bg-gray-700 hover:bg-gray-600 p-4 rounded-lg text-center transition-colors <?php echo $code === $current_lang ? 'ring-2 ring-red-500' : ''; ?>">
                        <div class="text-2xl mb-2"><?php echo $info['flag']; ?></div>
                        <div class="text-sm font-bold"><?php echo $info['native_name']; ?></div>
                        <div class="text-xs text-gray-400"><?php echo $code; ?></div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>

    </div>
</div>

<script>
// اختبار JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Multi-language system test loaded');
    console.log('Current language:', '<?php echo $current_lang; ?>');
    console.log('Text direction:', '<?php echo $text_direction; ?>');
    console.log('Is RTL:', <?php echo $is_rtl ? 'true' : 'false'; ?>);
    
    // اختبار نسخ النص
    window.testCopy = function() {
        if (typeof LanguageManager !== 'undefined') {
            LanguageManager.copyToClipboard('Test text copied!');
        } else {
            alert('LanguageManager not loaded');
        }
    };
    
    // اختبار الإشعارات
    window.testNotification = function() {
        if (typeof LanguageManager !== 'undefined') {
            LanguageManager.showNotification('Test notification message', 'success');
        } else {
            alert('LanguageManager not loaded');
        }
    };
});
</script>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>
