<?php
/**
 * صفحة تكية خيرية - إنجازات ومشاريع التكية
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// معلومات الصفحة
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

// جلب الإنجازات والمشاريع من قاعدة البيانات (الأحدث أولاً)
$achievements = selectQuery("
    SELECT * FROM achievements
    WHERE is_active = 1
    ORDER BY achievement_date DESC, created_at DESC
") ?: [];

// جلب إحصائيات التكية
$kitchen_stats = selectOne("
    SELECT 
        SUM(CASE WHEN package_type LIKE '%meal%' THEN 1 ELSE 0 END) as meals_served,
        SUM(CASE WHEN package_type LIKE '%family%' THEN 1 ELSE 0 END) as families_helped,
        COUNT(DISTINCT donor_email) as total_donors,
        SUM(CASE WHEN status = 'confirmed' THEN amount ELSE 0 END) as total_raised
    FROM donations
") ?: [
    'meals_served' => 15420,
    'families_helped' => 8750,
    'total_donors' => 2840,
    'total_raised' => 125000
];

$page_title = __('kitchen_title');
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #111;
            color: #f0f0f0;
        }
        .achievement-card {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            border: 1px solid #374151;
            transition: all 0.3s ease;
        }
        .achievement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(230, 57, 70, 0.2);
            border-color: #e63946;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                    <i class="fas fa-heart text-white text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"><?php echo __('site_name'); ?></h1>
            </div>
            <nav class="hidden md:block">
                <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                    <li><a href="index.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_home'); ?></a></li>
                    <li><a href="charity_kitchen.php" class="text-red-500 font-medium"><?php echo __('nav_kitchen'); ?></a></li>
                    <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_tragedy'); ?></a></li>
                    <li><a href="donate.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_donate'); ?></a></li>
                    <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_testimonials'); ?></a></li>
                    <li><a href="contact.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_contact'); ?></a></li>
                </ul>
            </nav>

            <!-- Language Selector -->
            <div class="flex items-center">
                <div class="language-selector-header">
                    <?php echo renderLanguageSelector('header-style'); ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-red-900 to-red-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-utensils text-6xl text-yellow-400"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('kitchen_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('kitchen_description'); ?></p>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-400"><?php echo number_format($kitchen_stats['meals_served']); ?></div>
                    <div class="text-sm"><?php echo __('stats_meals'); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-400"><?php echo number_format($kitchen_stats['families_helped']); ?></div>
                    <div class="text-sm"><?php echo __('stats_families'); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-400"><?php echo number_format($kitchen_stats['total_donors']); ?></div>
                    <div class="text-sm"><?php echo __('kitchen_donors'); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-400">$<?php echo number_format($kitchen_stats['total_raised']); ?></div>
                    <div class="text-sm"><?php echo __('kitchen_total_raised'); ?></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Mission -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('kitchen_mission_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('kitchen_mission_desc'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-utensils text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('kitchen_feed_hungry'); ?></h3>
                    <p class="text-gray-400"><?php echo __('kitchen_feed_desc'); ?></p>
                </div>
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-home text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('kitchen_shelter_homeless'); ?></h3>
                    <p class="text-gray-400"><?php echo __('kitchen_shelter_desc'); ?></p>
                </div>
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-tshirt text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('kitchen_clothe_needy'); ?></h3>
                    <p class="text-gray-400"><?php echo __('kitchen_clothe_desc'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('kitchen_achievements_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('kitchen_achievements_desc'); ?></p>
            </div>

            <?php if (!empty($achievements)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($achievements as $achievement): ?>
                        <div class="achievement-card p-6 rounded-lg">
                            <?php if (!empty($achievement['image_url'])): ?>
                                <img src="<?php echo htmlspecialchars($achievement['image_url']); ?>" alt="<?php echo htmlspecialchars($achievement['title']); ?>" class="w-full h-48 object-cover rounded-lg mb-4">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                                    <i class="fas fa-image text-4xl text-gray-500"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center mb-2">
                                <i class="fas fa-calendar text-red-500 mr-2"></i>
                                <span class="text-gray-400 text-sm"><?php echo date('Y/m/d', strtotime($achievement['achievement_date'])); ?></span>
                            </div>
                            
                            <h3 class="text-xl font-bold text-white mb-3"><?php echo htmlspecialchars($achievement['title']); ?></h3>
                            <p class="text-gray-300 mb-4"><?php echo htmlspecialchars($achievement['description']); ?></p>
                            
                            <?php if (!empty($achievement['stats'])): ?>
                                <div class="bg-gray-700 p-3 rounded text-sm">
                                    <strong class="text-red-400"><?php echo __('statistics'); ?>:</strong>
                                    <span class="text-gray-300"><?php echo htmlspecialchars($achievement['stats']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <!-- إنجازات افتراضية -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="achievement-card p-6 rounded-lg">
                        <div class="w-full h-48 bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                            <i class="fas fa-utensils text-4xl text-red-500"></i>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calendar text-red-500 mr-2"></i>
                            <span class="text-gray-400 text-sm">2024/01/15</span>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">مشروع الوجبات الساخنة</h3>
                        <p class="text-gray-300 mb-4">تم توزيع أكثر من 15,000 وجبة ساخنة على العائلات المحتاجة في غزة خلال الشهر الماضي</p>
                        <div class="bg-gray-700 p-3 rounded text-sm">
                            <strong class="text-red-400"><?php echo __('statistics'); ?>:</strong>
                            <span class="text-gray-300">15,420 وجبة - 3,200 عائلة مستفيدة</span>
                        </div>
                    </div>

                    <div class="achievement-card p-6 rounded-lg">
                        <div class="w-full h-48 bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                            <i class="fas fa-home text-4xl text-red-500"></i>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calendar text-red-500 mr-2"></i>
                            <span class="text-gray-400 text-sm">2024/01/10</span>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">مشروع الإيواء الطارئ</h3>
                        <p class="text-gray-300 mb-4">توفير مأوى آمن لأكثر من 500 عائلة فقدت منازلها بسبب القصف</p>
                        <div class="bg-gray-700 p-3 rounded text-sm">
                            <strong class="text-red-400"><?php echo __('statistics'); ?>:</strong>
                            <span class="text-gray-300">500 عائلة - 2,100 فرد</span>
                        </div>
                    </div>

                    <div class="achievement-card p-6 rounded-lg">
                        <div class="w-full h-48 bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                            <i class="fas fa-tshirt text-4xl text-red-500"></i>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calendar text-red-500 mr-2"></i>
                            <span class="text-gray-400 text-sm">2024/01/05</span>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">حملة الشتاء الدافئ</h3>
                        <p class="text-gray-300 mb-4">توزيع البطانيات والملابس الشتوية للأطفال والعائلات</p>
                        <div class="bg-gray-700 p-3 rounded text-sm">
                            <strong class="text-red-400"><?php echo __('statistics'); ?>:</strong>
                            <span class="text-gray-300">1,200 بطانية - 800 طقم ملابس</span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-red-900">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-white mb-4"><?php echo __('kitchen_cta_title'); ?></h2>
            <p class="text-xl text-gray-200 mb-8"><?php echo __('kitchen_cta_desc'); ?></p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="donate.php" class="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-8 rounded-lg text-lg">
                    <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'mr-2' : 'ml-2'; ?>"></i><?php echo __('donate_now'); ?>
                </a>
                <a href="contact.php" class="border-2 border-white text-white hover:bg-white hover:text-red-900 font-bold py-3 px-8 rounded-lg text-lg">
                    <i class="fas fa-envelope <?php echo $is_rtl ? 'mr-2' : 'ml-2'; ?>"></i><?php echo __('nav_contact'); ?>
                </a>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
