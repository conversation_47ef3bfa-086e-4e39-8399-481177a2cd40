<?php
/**
 * ملف الاتصال بقاعدة البيانات
 */

// منع الوصول المباشر
if (!defined('CHARITY_GAZA')) {
    die('Access denied');
}

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // منع استنساخ الكائن
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * دالة للحصول على اتصال قاعدة البيانات
 */
function getDB() {
    return Database::getInstance()->getConnection();
}

/**
 * دالة لتنفيذ استعلام SELECT
 */
function selectQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        logError("Select query error: " . $e->getMessage(), $sql, $params);
        return false;
    }
}

/**
 * دالة لتنفيذ استعلام SELECT لسجل واحد
 */
function selectOne($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    } catch (PDOException $e) {
        logError("Select one query error: " . $e->getMessage(), $sql, $params);
        return false;
    }
}

/**
 * دالة لتنفيذ استعلام INSERT
 */
function insertQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $result = $stmt->execute($params);
        return $result ? $db->lastInsertId() : false;
    } catch (PDOException $e) {
        logError("Insert query error: " . $e->getMessage(), $sql, $params);
        return false;
    }
}

/**
 * دالة لتنفيذ استعلام UPDATE
 */
function updateQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->rowCount();
    } catch (PDOException $e) {
        logError("Update query error: " . $e->getMessage(), $sql, $params);
        return false;
    }
}

/**
 * دالة لتنفيذ استعلام DELETE
 */
function deleteQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->rowCount();
    } catch (PDOException $e) {
        logError("Delete query error: " . $e->getMessage(), $sql, $params);
        return false;
    }
}

/**
 * دالة لبدء معاملة قاعدة البيانات
 */
function beginTransaction() {
    try {
        return getDB()->beginTransaction();
    } catch (PDOException $e) {
        logError("Begin transaction error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لتأكيد المعاملة
 */
function commitTransaction() {
    try {
        return getDB()->commit();
    } catch (PDOException $e) {
        logError("Commit transaction error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لإلغاء المعاملة
 */
function rollbackTransaction() {
    try {
        return getDB()->rollback();
    } catch (PDOException $e) {
        logError("Rollback transaction error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $sql = '', $params = []) {
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if (!empty($sql)) {
        $logMessage .= " | SQL: " . $sql;
    }
    if (!empty($params)) {
        $logMessage .= " | Params: " . json_encode($params);
    }
    $logMessage .= " | IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown');
    $logMessage .= " | User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown');
    $logMessage .= PHP_EOL;
    
    error_log($logMessage, 3, __DIR__ . '/../logs/database_errors.log');
}

/**
 * دالة لتنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة للتحقق من وجود جدول
 */
function tableExists($tableName) {
    $sql = "SHOW TABLES LIKE ?";
    $result = selectOne($sql, [$tableName]);
    return !empty($result);
}

/**
 * دالة لإنشاء مجلد السجلات إذا لم يكن موجوداً
 */
if (!file_exists(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0755, true);
}
?>
