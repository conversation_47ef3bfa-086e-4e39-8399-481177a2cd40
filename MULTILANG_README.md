# نظام اللغات المتعددة - Multi-Language System

## نظرة عامة / Overview

تم تطوير نظام متعدد اللغات شامل لموقع "يداً بيد لأجل غزة" يدعم 13 لغة مختلفة مع الإنجليزية كلغة افتراضية، بينما تبقى لوحة الإدارة باللغة العربية فقط.

A comprehensive multi-language system has been developed for the "Hand in Hand for Gaza" website supporting 13 different languages with English as the default language, while the admin panel remains Arabic-only.

## اللغات المدعومة / Supported Languages

✅ 🇺🇸 **الإنجليزية (English)** - افتراضية / Default  
✅ 🇸🇦 **العربية (Arabic)**  
✅ 🇷🇺 **الروسية (Russian)**  
✅ 🇩🇪 **الألمانية (German)**  
✅ 🇸🇪 **السويدية (Swedish)**  
✅ 🇨🇳 **الصينية (Chinese)**  
✅ 🇳🇴 **النرويجية (Norwegian)**  
✅ 🇧🇷 **البرتغالية (Portuguese)**  
✅ 🇹🇷 **التركية (Turkish)**  
✅ 🇪🇸 **الإسبانية (Spanish)**  
✅ 🇮🇩 **الإندونيسية (Indonesian)**  
✅ 🇯🇵 **اليابانية (Japanese)**  
✅ 🇲🇾 **الماليزية (Malaysian)**

## هيكل الملفات / File Structure

```
charity_gaza/
├── includes/
│   ├── language_manager.php    # نظام إدارة اللغات
│   ├── config.php             # إعدادات اللغات
│   └── header.php             # دعم اللغات في الهيدر
├── languages/                 # ملفات الترجمة
│   ├── en.php                # الإنجليزية (افتراضية)
│   ├── ar.php                # العربية
│   ├── ru.php                # الروسية
│   ├── de.php                # الألمانية
│   ├── sv.php                # السويدية
│   ├── zh.php                # الصينية
│   ├── no.php                # النرويجية
│   ├── pt.php                # البرتغالية
│   ├── tr.php                # التركية
│   ├── es.php                # الإسبانية
│   ├── id.php                # الإندونيسية
│   ├── ja.php                # اليابانية
│   └── ms.php                # الماليزية
├── assets/
│   ├── css/
│   │   └── multilang.css     # أنماط اللغات المتعددة
│   └── js/
│       └── multilang.js      # وظائف اللغات المتعددة
└── index_multilang.php       # صفحة رئيسية محدثة
```

## المميزات / Features

### 1. نظام الترجمة / Translation System
- **دوال مساعدة**: `__()`, `getCurrentLanguage()`, `getTextDirection()`, `isRTL()`
- **ترجمة ديناميكية**: جميع النصوص قابلة للترجمة
- **دعم المتغيرات**: إمكانية تمرير متغيرات للنصوص المترجمة

### 2. دعم الاتجاهات / Direction Support
- **RTL**: العربية (من اليمين لليسار)
- **LTR**: باقي اللغات (من اليسار لليمين)
- **تخطيط تلقائي**: تغيير التخطيط حسب اتجاه اللغة

### 3. منتقي اللغة / Language Selector
- **موقع ثابت**: في أعلى الصفحة
- **تصميم جذاب**: مع أعلام الدول والأسماء المحلية
- **سهولة الاستخدام**: قائمة منسدلة تفاعلية

### 4. دعم الخطوط / Font Support
- **العربية**: Tajawal
- **الصينية**: Noto Sans SC
- **اليابانية**: Noto Sans JP
- **باقي اللغات**: Inter

### 5. لوحة الإدارة / Admin Panel
- **عربية فقط**: لوحة الإدارة تبقى باللغة العربية
- **كشف تلقائي**: النظام يكشف تلقائياً إذا كان المستخدم في لوحة الإدارة

## كيفية الاستخدام / How to Use

### 1. في ملفات PHP

```php
// الحصول على نص مترجم
echo __('site_name');

// التحقق من اللغة الحالية
if (getCurrentLanguage() === 'ar') {
    // كود خاص بالعربية
}

// التحقق من الاتجاه
if (isRTL()) {
    // كود خاص بـ RTL
}

// عرض منتقي اللغة
echo renderLanguageSelector('custom-class');
```

### 2. في ملفات HTML

```html
<!-- نص قابل للترجمة -->
<h1><?php echo __('hero_title'); ?></h1>

<!-- دعم الاتجاه -->
<div class="<?php echo $is_rtl ? 'text-right' : 'text-left'; ?>">
    <?php echo __('description'); ?>
</div>

<!-- أيقونة مع دعم الاتجاه -->
<i class="fas fa-arrow-right <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
```

### 3. في ملفات CSS

```css
/* دعم الاتجاهات */
[dir="rtl"] .my-element {
    margin-right: 10px;
    margin-left: 0;
}

[dir="ltr"] .my-element {
    margin-left: 10px;
    margin-right: 0;
}

/* دعم اللغات */
html[lang="ar"] {
    font-family: 'Tajawal', sans-serif;
}

html[lang="zh"] {
    font-family: 'Noto Sans SC', sans-serif;
}
```

### 4. في ملفات JavaScript

```javascript
// الحصول على اللغة الحالية
const currentLang = LanguageManager.getCurrentLanguage();

// التحقق من الاتجاه
if (LanguageManager.isRTL()) {
    // كود خاص بـ RTL
}

// إظهار إشعار
LanguageManager.showNotification('رسالة', 'success');

// نسخ للحافظة
LanguageManager.copyToClipboard('نص للنسخ');
```

## إضافة لغة جديدة / Adding a New Language

### 1. إنشاء ملف الترجمة

```php
// languages/xx.php
<?php
$translations = [
    'site_name' => 'اسم الموقع بالغة الجديدة',
    'nav_home' => 'الرئيسية',
    // ... باقي الترجمات
];
?>
```

### 2. تحديث الإعدادات

```php
// includes/config.php
$supported_languages['xx'] = [
    'name' => 'Language Name',
    'native_name' => 'اسم اللغة المحلي',
    'flag' => '🏁',
    'dir' => 'ltr' // أو 'rtl'
];
```

### 3. إضافة دعم الخط (اختياري)

```css
/* assets/css/multilang.css */
html[lang="xx"] {
    font-family: 'Font Name', sans-serif;
}
```

## الإعدادات / Configuration

### متغيرات مهمة في config.php

```php
// اللغة الافتراضية
define('DEFAULT_LANGUAGE', 'en');

// لغة لوحة الإدارة
define('ADMIN_LANGUAGE', 'ar');

// اللغات المدعومة
$supported_languages = [...];
```

### إعدادات الجلسة

- **حفظ اللغة**: يتم حفظ اختيار المستخدم في الجلسة
- **كشف تلقائي**: كشف لوحة الإدارة تلقائياً
- **معاملات URL**: دعم `?lang=xx` لتغيير اللغة

## الاختبار / Testing

### 1. اختبار اللغات
- تصفح الموقع بكل لغة
- التأكد من صحة الترجمات
- اختبار منتقي اللغة

### 2. اختبار الاتجاهات
- التأكد من صحة RTL للعربية
- اختبار LTR لباقي اللغات
- فحص التخطيط والأيقونات

### 3. اختبار لوحة الإدارة
- التأكد من بقاء العربية في الإدارة
- اختبار عدم تأثر لوحة الإدارة بتغيير اللغة

## الصيانة / Maintenance

### 1. إضافة ترجمات جديدة
- إضافة المفاتيح الجديدة لجميع ملفات اللغات
- اختبار الترجمات الجديدة

### 2. تحديث الترجمات
- مراجعة دورية للترجمات
- تصحيح الأخطاء اللغوية

### 3. تحسين الأداء
- ضغط ملفات CSS و JavaScript
- تحسين تحميل الخطوط

## المشاكل الشائعة / Common Issues

### 1. عدم ظهور الترجمة
- التأكد من وجود المفتاح في ملف اللغة
- فحص صحة اسم ملف اللغة

### 2. مشاكل الاتجاه
- التأكد من إعداد `dir` في إعدادات اللغة
- فحص CSS للاتجاهات

### 3. مشاكل الخطوط
- التأكد من تحميل الخطوط
- فحص إعدادات CSS للخطوط

## الدعم / Support

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- مراجعة هذا الدليل
- فحص ملفات السجل (logs)
- التواصل مع فريق التطوير

---

**ملاحظة**: هذا النظام قابل للتوسع ويمكن إضافة المزيد من اللغات بسهولة في المستقبل.

**Note**: This system is scalable and more languages can be easily added in the future.
