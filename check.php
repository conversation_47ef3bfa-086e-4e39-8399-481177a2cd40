<?php
/**
 * ملف فحص النظام والمتطلبات
 */

echo "<!DOCTYPE html>
<html lang='<?php echo $current_lang; ?>' dir='<?php echo $text_direction; ?>'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص النظام - يداً بيد لأجل غزة</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Tajawal', sans-serif; background: linear-gradient(135deg, #1f2937 0%, #111827 100%); }
    </style>
</head>
<body class='min-h-screen p-4'>
    <div class='max-w-4xl mx-auto'>
        <div class='bg-gray-800 p-8 rounded-xl shadow-2xl'>
            <h1 class='text-3xl font-bold text-red-500 mb-6 text-center'>فحص النظام والمتطلبات</h1>
            <div class='space-y-4'>";

// فحص إصدار PHP
echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>🔍 إصدار PHP</h3>
        <p class='text-gray-300'>الإصدار الحالي: <span class='text-green-400 font-bold'>" . PHP_VERSION . "</span></p>";
        
if (version_compare(PHP_VERSION, '8.0.0', '>=')) {
    echo "<p class='text-green-400'>✅ إصدار PHP مناسب</p>";
} else {
    echo "<p class='text-red-400'>❌ يتطلب PHP 8.0 أو أحدث</p>";
}
echo "</div>";

// فحص إضافات PHP المطلوبة
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'curl'];
echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>🔧 إضافات PHP المطلوبة</h3>";

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p class='text-green-400'>✅ $ext</p>";
    } else {
        echo "<p class='text-red-400'>❌ $ext (غير مثبت)</p>";
    }
}
echo "</div>";

// فحص الاتصال بـ MySQL
echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>🗄️ اختبار الاتصال بـ MySQL</h3>";

try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p class='text-green-400'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // فحص قاعدة البيانات
    $databases = $pdo->query("SHOW DATABASES LIKE 'charity_gaza'")->fetchAll();
    if (count($databases) > 0) {
        echo "<p class='text-green-400'>✅ قاعدة البيانات charity_gaza موجودة</p>";
        
        // فحص الجداول
        $pdo->exec("USE charity_gaza");
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        $required_tables = ['donors', 'donations', 'counters', 'testimonials', 'contacts', 'admin_users'];
        $missing_tables = array_diff($required_tables, $tables);
        
        if (empty($missing_tables)) {
            echo "<p class='text-green-400'>✅ جميع الجداول المطلوبة موجودة</p>";
        } else {
            echo "<p class='text-yellow-400'>⚠️ الجداول المفقودة: " . implode(', ', $missing_tables) . "</p>";
        }
    } else {
        echo "<p class='text-yellow-400'>⚠️ قاعدة البيانات charity_gaza غير موجودة</p>";
    }
    
} catch (PDOException $e) {
    echo "<p class='text-red-400'>❌ فشل الاتصال بـ MySQL: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// فحص الملفات المطلوبة
$required_files = [
    'includes/config.php',
    'includes/db.php', 
    'includes/functions.php',
    'assets/css/style.css',
    'assets/js/main.js',
    'admin/login.php'
];

echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>📁 الملفات المطلوبة</h3>";

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p class='text-green-400'>✅ $file</p>";
    } else {
        echo "<p class='text-red-400'>❌ $file (غير موجود)</p>";
    }
}
echo "</div>";

// فحص صلاحيات المجلدات
$writable_dirs = ['logs'];
echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>📝 صلاحيات الكتابة</h3>";

foreach ($writable_dirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "<p class='text-green-400'>✅ $dir (قابل للكتابة)</p>";
    } else {
        echo "<p class='text-red-400'>❌ $dir (غير قابل للكتابة أو غير موجود)</p>";
    }
}
echo "</div>";

// معلومات الخادم
echo "<div class='bg-gray-700 p-4 rounded'>
        <h3 class='font-bold text-white mb-2'>🖥️ معلومات الخادم</h3>
        <p class='text-gray-300'>نظام التشغيل: <span class='text-blue-400'>" . PHP_OS . "</span></p>
        <p class='text-gray-300'>خادم الويب: <span class='text-blue-400'>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "</span></p>
        <p class='text-gray-300'>المجلد الحالي: <span class='text-blue-400'>" . __DIR__ . "</span></p>
        <p class='text-gray-300'>الرابط الحالي: <span class='text-blue-400'>" . ($_SERVER['HTTP_HOST'] ?? 'localhost') . $_SERVER['REQUEST_URI'] . "</span></p>
      </div>";

// خطوات الحل
echo "<div class='bg-blue-700 p-4 rounded mt-6'>
        <h3 class='font-bold text-white mb-2'>🔧 خطوات الحل</h3>
        <ol class='list-decimal list-inside space-y-2 text-gray-200'>
            <li>تأكد من تشغيل XAMPP (Apache + MySQL)</li>
            <li>إذا كانت قاعدة البيانات غير موجودة، شغّل <a href='setup.php' class='text-blue-300 hover:underline'>setup.php</a></li>
            <li>تأكد من وضع المشروع في مجلد htdocs</li>
            <li>تأكد من إعدادات قاعدة البيانات في includes/config.php</li>
            <li>إذا استمرت المشاكل، تحقق من سجل أخطاء Apache</li>
        </ol>
      </div>";

echo "    </div>
    </div>
</body>
</html>";
?>
