<?php
/**
 * الصفحة الرئيسية لموقع يداً بيد لأجل غزة
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'submit_donation':
            $response = processDonation($_POST);
            echo json_encode($response);
            exit;
            
        case 'submit_contact':
            $response = processContact($_POST);
            echo json_encode($response);
            exit;
            
        case 'get_counters':
            $response = getCounters();
            echo json_encode($response);
            exit;
    }
}

// دالة معالجة التبرعات
function processDonation($data) {
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['email']) || empty($data['amount']) || empty($data['package'])) {
        return ['success' => false, 'message' => 'جميع الحقول مطلوبة'];
    }
    
    if (!validateEmail($data['email'])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
    }
    
    $amount = floatval($data['amount']);
    if ($amount < MIN_DONATION_AMOUNT || $amount > MAX_DONATION_AMOUNT) {
        return ['success' => false, 'message' => 'مبلغ التبرع غير صحيح'];
    }
    
    // تنظيف البيانات
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $package = sanitizeInput($data['package']);
    $txid = !empty($data['txid']) ? sanitizeInput($data['txid']) : null;
    
    // إدراج التبرع مباشرة (بدون جدول donors منفصل)
    $donationId = insertQuery(
        "INSERT INTO donations (donor_name, donor_email, package_type, amount, txid, status, created_at)
         VALUES (?, ?, ?, ?, ?, 'pending', NOW())",
        [$name, $email, $package, $amount, $txid]
    );
    
    if ($donationId) {
        // إرسال تأكيد بالبريد
        sendDonationConfirmation($email, $name, $amount, $package, $txid);
        
        return ['success' => true, 'message' => 'تم إرسال طلب التبرع بنجاح'];
    } else {
        return ['success' => false, 'message' => 'حدث خطأ أثناء معالجة التبرع'];
    }
}

// دالة معالجة رسائل التواصل
function processContact($data) {
    if (empty($data['name']) || empty($data['email']) || empty($data['message'])) {
        return ['success' => false, 'message' => 'جميع الحقول مطلوبة'];
    }
    
    if (!validateEmail($data['email'])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
    }
    
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $subject = sanitizeInput($data['subject'] ?? 'رسالة عامة');
    $message = sanitizeInput($data['message']);
    
    $contactId = insertQuery(
        "INSERT INTO contacts (name, email, subject, message) VALUES (?, ?, ?, ?)",
        [$name, $email, $subject, $message]
    );
    
    if ($contactId) {
        return ['success' => true, 'message' => 'تم إرسال رسالتك بنجاح'];
    } else {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إرسال الرسالة'];
    }
}

// دالة جلب العدادات
function getCounters() {
    $counters = selectOne("SELECT * FROM counters ORDER BY id DESC LIMIT 1");
    
    if ($counters) {
        return [
            'success' => true,
            'data' => [
                'meals' => intval($counters['meals_count']),
                'families' => intval($counters['families_count']),
                'children' => intval($counters['children_count']),
                'donors' => intval($counters['donors_count'])
            ]
        ];
    } else {
        return [
            'success' => true,
            'data' => [
                'meals' => 15420,
                'families' => 8750,
                'children' => 12300,
                'donors' => 2840
            ]
        ];
    }
}

// جلب الشهادات (الأحدث أولاً)
$testimonials = selectQuery("SELECT * FROM testimonials WHERE is_active = 1 ORDER BY testimonial_date DESC, created_at DESC LIMIT 6");

// جلب إحصائيات التبرعات
$donationStats = selectOne("
    SELECT 
        COUNT(*) as total_donations,
        SUM(CASE WHEN status = 'confirmed' THEN amount ELSE 0 END) as confirmed_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
    FROM donations
");

// معلومات الصفحة
$page_title = __('nav_home');
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

// تضمين الهيدر
include 'includes/header.php';
?>

    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4">
            <!-- الصف الأول: اللوجو والتنقل ومنتقي اللغة -->
            <div class="flex justify-between items-center">
                <!-- اللوجو -->
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"><?php echo __('site_name'); ?></h1>
                </div>

                <!-- التنقل الرئيسي -->
                <nav class="hidden lg:block">
                    <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                        <li><a href="index.php" class="text-red-500 font-medium hover:text-red-300 transition-colors"><?php echo __('nav_home'); ?></a></li>
                        <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_kitchen'); ?></a></li>
                        <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_tragedy'); ?></a></li>
                        <li><a href="donate.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_donate'); ?></a></li>
                        <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_testimonials'); ?></a></li>
                        <li><a href="contact.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_contact'); ?></a></li>
                    </ul>
                </nav>

                <!-- منتقي اللغة وقائمة الموبايل -->
                <div class="flex items-center space-x-4 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                    <!-- منتقي اللغة -->
                    <div class="language-selector-header">
                        <?php echo renderLanguageSelector('header-style'); ?>
                    </div>

                    <!-- زر قائمة الموبايل -->
                    <div class="lg:hidden">
                        <button id="mobile-menu-btn" class="text-white hover:text-red-500 transition-colors">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden lg:hidden bg-gray-900 border-t border-red-900">
            <div class="py-4">
                <!-- روابط التنقل -->
                <ul class="mb-4">
                    <li><a href="index.php" class="block px-4 py-3 text-red-500 hover:bg-gray-800 transition-colors"><?php echo __('nav_home'); ?></a></li>
                    <li><a href="charity_kitchen.php" class="block px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"><?php echo __('nav_kitchen'); ?></a></li>
                    <li><a href="tragedy.php" class="block px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"><?php echo __('nav_tragedy'); ?></a></li>
                    <li><a href="donate.php" class="block px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"><?php echo __('nav_donate'); ?></a></li>
                    <li><a href="testimonials.php" class="block px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"><?php echo __('nav_testimonials'); ?></a></li>
                    <li><a href="contact.php" class="block px-4 py-3 text-gray-300 hover:bg-gray-800 transition-colors"><?php echo __('nav_contact'); ?></a></li>
                </ul>

                <!-- منتقي اللغة للموبايل -->
                <div class="border-t border-gray-700 pt-4 px-4">
                    <p class="text-gray-400 text-sm mb-3"><?php echo __('select_language', 'Select Language'); ?>:</p>
                    <div class="language-selector-mobile">
                        <?php echo renderLanguageSelector('mobile-style'); ?>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-bg text-white py-20 md:py-32">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-heartbeat crying-icon text-5xl"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('hero_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('hero_description'); ?></p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="donate.php" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full text-lg pulse transition-colors">
                    <?php echo __('donate_now'); ?> <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                </a>
                <a href="#about" class="border-2 border-white text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-full text-lg transition-all">
                    <?php echo __('learn_more'); ?>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-black text-white py-12 border-t border-b border-red-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('stats_title'); ?></h2>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="mealsCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_meals'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="familiesCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_families'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="sheltersCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_shelters'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="medicalCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_medical'); ?></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('about_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('about_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mission-vision-cards">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bullseye text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_mission'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_mission_desc'); ?></p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-eye text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_vision'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_vision_desc'); ?></p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-heart text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_values'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_values_desc'); ?></p>
                </div>
            </div>

            <div class="tragic-section">
                <div class="flex flex-col md:flex-row items-center gap-8">
                    <div class="md:w-1/2">
                        <div class="tragic-image-container">
                            <img src="https://www.middleeasteye.net/sites/default/files/styles/max_2600x2600/public/images-story/gaza_famine_reutersmarch19.jpg.jpg?itok=mnkplyzR"
                                 alt="Gaza famine"
                                 class="tragic-image" />
                        </div>
                    </div>
                    <div class="md:w-1/2 tragic-content">
                        <h3 class="text-2xl font-bold text-red-500 mb-6"><?php echo __('tragedy_title'); ?></h3>
                        <p class="mb-6 leading-relaxed text-gray-300">
                            <?php echo __('tragedy_description_1'); ?>
                        </p>
                        <p class="mb-8 leading-relaxed text-gray-300">
                            <?php echo __('tragedy_description_2'); ?>
                        </p>
                        <div class="shocking-facts-box">
                            <h3 class="font-bold text-xl mb-4 text-red-500"><i class="fas fa-exclamation-triangle <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i> <?php echo __('shocking_facts'); ?></h3>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <i class="fas fa-skull text-red-500 mt-1 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                                    <span><?php echo __('fact_1'); ?></span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-home text-red-500 mt-1 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                                    <span><?php echo __('fact_2'); ?></span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-hospital text-red-500 mt-1 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                                    <span><?php echo __('fact_3'); ?></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Donation Packages Section -->
    <section id="donate" class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('donation_packages'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('hero_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- وجبة ساخنة -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-utensils text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_meal'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_meal_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$5</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <a href="donate.php?package=meal&amount=5" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-center block transition-colors">
                        <?php echo __('donate_now'); ?>
                    </a>
                </div>

                <!-- مساعدة عائلة -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-home text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_family_aid'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_family_aid_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$50</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <a href="donate.php?package=family_aid&amount=50" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-center block transition-colors">
                        <?php echo __('donate_now'); ?>
                    </a>
                </div>

                <!-- مأوى آمن -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-shield-alt text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_shelter'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_shelter_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$100</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <a href="donate.php?package=shelter&amount=100" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-center block transition-colors">
                        <?php echo __('donate_now'); ?>
                    </a>
                </div>
            </div>

            <!-- Wallet Information -->
            <div class="bg-gray-700 p-8 rounded-xl shadow-md max-w-3xl mx-auto border border-red-900">
                <h3 class="text-xl font-bold text-white mb-6 text-center"><?php echo __('wallet_info'); ?></h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-300 mb-2"><?php echo __('wallet_address'); ?>:</label>
                        <div class="flex items-center">
                            <input type="text" value="<?php echo TRON_WALLET_ADDRESS; ?>" readonly class="flex-grow p-3 bg-gray-800 text-white rounded-l-lg border border-gray-600">
                            <button onclick="copyToClipboard('<?php echo TRON_WALLET_ADDRESS; ?>')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-r-lg">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-center">
                        <p class="text-gray-400 text-sm">
                            <i class="fas fa-info-circle <?php echo $is_rtl ? 'ml-1' : 'mr-1'; ?>"></i>
                            <?php echo __('payment_instructions'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('testimonials_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('testimonials_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php if (!empty($testimonials)): ?>
                    <?php foreach ($testimonials as $testimonial): ?>
                        <div class="testimonial-card p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    <?php echo mb_substr($testimonial['name'], 0, 1); ?>
                                </div>
                                <div class="mr-4">
                                    <h4 class="font-bold text-white"><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                    <p class="text-gray-400 text-sm"><?php echo htmlspecialchars($testimonial['role']); ?></p>
                                </div>
                            </div>
                            <p class="text-gray-300 leading-relaxed">
                                "<?php echo htmlspecialchars($testimonial['message']); ?>"
                            </p>
                            <div class="mt-4 flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- شهادات افتراضية في حالة عدم وجود بيانات -->
                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg"><?php echo __('testimonial_1_initial'); ?></div>
                            <div class="<?php echo $is_rtl ? 'ml-4' : 'mr-4'; ?>">
                                <h4 class="font-bold text-white"><?php echo __('testimonial_1_name'); ?></h4>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_1_role'); ?></p>
                            </div>
                        </div>
                        <p class="text-gray-300 leading-relaxed">
                            "<?php echo __('testimonial_1_message'); ?>"
                        </p>
                        <div class="mt-4 flex text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>

                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg"><?php echo __('testimonial_2_initial'); ?></div>
                            <div class="<?php echo $is_rtl ? 'ml-4' : 'mr-4'; ?>">
                                <h4 class="font-bold text-white"><?php echo __('testimonial_2_name'); ?></h4>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_2_role'); ?></p>
                            </div>
                        </div>
                        <p class="text-gray-300 leading-relaxed">
                            "<?php echo __('testimonial_2_message'); ?>"
                        </p>
                        <div class="mt-4 flex text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>

                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg"><?php echo __('testimonial_3_initial'); ?></div>
                            <div class="<?php echo $is_rtl ? 'ml-4' : 'mr-4'; ?>">
                                <h4 class="font-bold text-white"><?php echo __('testimonial_3_name'); ?></h4>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_3_role'); ?></p>
                            </div>
                        </div>
                        <p class="text-gray-300 leading-relaxed">
                            "<?php echo __('testimonial_3_message'); ?>"
                        </p>
                        <div class="mt-4 flex text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('contact_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('contact_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('contact_info'); ?></h3>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-map-marker-alt text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('address_label'); ?></h4>
                                <p class="text-gray-400"><?php echo __('address_text'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-phone-alt text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('emergency_phone'); ?></h4>
                                <p class="text-gray-400"><?php echo __('phone_number'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('email_receipts'); ?></h4>
                                <p class="text-gray-400"><?php echo FROM_EMAIL; ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-wallet text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('donation_wallet'); ?></h4>
                                <p class="text-green-400 font-mono text-sm break-all"><?php echo TRON_WALLET_ADDRESS; ?></p>
                                <button onclick="copyToClipboard('<?php echo TRON_WALLET_ADDRESS; ?>')" class="mt-2 text-red-400 hover:text-red-300 text-sm">
                                    <i class="fas fa-copy <?php echo $is_rtl ? 'ml-1' : 'mr-1'; ?>"></i> <?php echo __('copy_address'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <form id="contactForm" class="contact-form p-6 rounded-lg">
                        <div class="mb-4">
                            <label for="contactName" class="block text-white font-medium mb-2"><?php echo __('full_name'); ?></label>
                            <input type="text" id="contactName" name="name" class="w-full p-3 rounded-lg" required>
                        </div>
                        <div class="mb-4">
                            <label for="contactEmail" class="block text-white font-medium mb-2"><?php echo __('email_address'); ?></label>
                            <input type="email" id="contactEmail" name="email" class="w-full p-3 rounded-lg" required>
                        </div>
                        <div class="mb-4">
                            <label for="contactSubject" class="block text-white font-medium mb-2"><?php echo __('subject'); ?></label>
                            <select id="contactSubject" name="subject" class="w-full p-3 rounded-lg bg-gray-800 text-white border border-gray-600">
                                <option value="donation_receipt"><?php echo __('subject_donation_receipt'); ?></option>
                                <option value="general_inquiry"><?php echo __('subject_general_inquiry'); ?></option>
                                <option value="volunteer"><?php echo __('subject_volunteer'); ?></option>
                                <option value="complaint"><?php echo __('subject_complaint'); ?></option>
                                <option value="other"><?php echo __('subject_other'); ?></option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label for="contactMessage" class="block text-white font-medium mb-2"><?php echo __('your_message'); ?></label>
                            <textarea id="contactMessage" name="message" rows="5" class="w-full p-3 rounded-lg resize-none" placeholder="<?php echo __('message_placeholder'); ?>" required></textarea>
                        </div>
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg donate-btn">
                            <span class="btn-text"><?php echo __('send_message'); ?></span>
                            <span class="btn-loading hidden">
                                <span class="loading"></span> <?php echo __('sending'); ?>
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <script>
        // دالة نسخ النص للحافظة
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    if (typeof LanguageManager !== 'undefined') {
                        LanguageManager.showNotification('<?php echo __("copied_to_clipboard", "Copied to clipboard"); ?>', 'success');
                    } else {
                        alert('<?php echo __("copied_to_clipboard", "Copied to clipboard"); ?>');
                    }
                }).catch(() => {
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        // دالة احتياطية للنسخ
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                if (typeof LanguageManager !== 'undefined') {
                    LanguageManager.showNotification('<?php echo __("copied_to_clipboard", "Copied to clipboard"); ?>', 'success');
                } else {
                    alert('<?php echo __("copied_to_clipboard", "Copied to clipboard"); ?>');
                }
            } catch (err) {
                if (typeof LanguageManager !== 'undefined') {
                    LanguageManager.showNotification('<?php echo __("copy_failed", "Copy failed"); ?>', 'error');
                } else {
                    alert('<?php echo __("copy_failed", "Copy failed"); ?>');
                }
            }

            document.body.removeChild(textArea);
        }

        // تبديل قائمة الموبايل
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

<?php
// تضمين الفوتر
include 'includes/footer.php';
?>
