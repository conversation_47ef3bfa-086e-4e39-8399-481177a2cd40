<?php
/**
 * ملف الوظائف المشتركة
 */

// منع الوصول المباشر
if (!defined('CHARITY_GAZA')) {
    die('Access denied');
}

/**
 * دالة لبدء الجلسة بشكل آمن
 */
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // تجديد معرف الجلسة لمنع session hijacking
    if (!isset($_SESSION['initiated'])) {
        session_regenerate_id(true);
        $_SESSION['initiated'] = true;
    }
    
    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)) {
        session_unset();
        session_destroy();
        return false;
    }
    
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * دالة للتحقق من تسجيل دخول المشرف
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_username']);
}

/**
 * دالة لتسجيل خروج المشرف
 */
function adminLogout() {
    session_unset();
    session_destroy();
    header('Location: ' . ADMIN_URL . '/login.php');
    exit;
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة للتحقق من صحة رقم الهاتف
 */
function validatePhone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

/**
 * دالة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة لإرسال البريد الإلكتروني
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    // هنا يمكن استخدام PHPMailer أو أي مكتبة أخرى
    // للبساطة، سنستخدم mail() function
    
    $headers = "From: " . FROM_NAME . " <" . FROM_EMAIL . ">\r\n";
    $headers .= "Reply-To: " . FROM_EMAIL . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    
    if ($isHTML) {
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $message, $headers);
}

/**
 * دالة لإرسال تأكيد التبرع
 */
function sendDonationConfirmation($donorEmail, $donorName, $amount, $package, $txid) {
    $subject = "تأكيد استلام طلب التبرع - يداً بيد لأجل غزة";
    
    $message = "
    <html dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #e63946; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f8f9fa; }
            .footer { padding: 20px; text-align: center; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>يداً بيد لأجل غزة</h1>
                <p>شكراً لك على تبرعك الكريم</p>
            </div>
            <div class='content'>
                <h2>عزيزي/عزيزتي {$donorName}</h2>
                <p>تم استلام طلب التبرع الخاص بك بنجاح. إليك تفاصيل التبرع:</p>
                <ul>
                    <li><strong>نوع التبرع:</strong> {$package}</li>
                    <li><strong>المبلغ:</strong> {$amount} USDT</li>
                    <li><strong>معرف المعاملة:</strong> {$txid}</li>
                    <li><strong>الحالة:</strong> قيد المراجعة</li>
                </ul>
                <p>سيتم التحقق من المعاملة خلال 24 ساعة وستصلك رسالة تأكيد نهائية.</p>
                <p>جزاك الله خيراً على مساعدة أهالي غزة.</p>
            </div>
            <div class='footer'>
                <p>يداً بيد لأجل غزة | charity-gaza.org</p>
            </div>
        </div>
    </body>
    </html>";
    
    return sendEmail($donorEmail, $subject, $message, true);
}

/**
 * دالة للتحقق من TXID عبر TRON API
 */
function verifyTronTransaction($txid, $expectedAmount, $toAddress = null) {
    if (empty($txid)) {
        return ['success' => false, 'message' => 'معرف المعاملة مطلوب'];
    }

    // التحقق من أن TXID ليس وهمي أو للاختبار
    if (strlen($txid) < 64 || preg_match('/^test/i', $txid) || preg_match('/^demo/i', $txid)) {
        return ['success' => false, 'message' => 'معرف المعاملة غير صحيح أو للاختبار فقط'];
    }

    $toAddress = $toAddress ?: TRON_WALLET_ADDRESS;

    try {
        // استدعاء TRON API للتحقق من المعاملة
        $url = TRON_API_URL . "/v1/transactions/{$txid}";

        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => TXID_VERIFICATION_TIMEOUT,
                'header' => 'Content-Type: application/json',
                'ignore_errors' => true
            ]
        ]);

        $response = file_get_contents($url, false, $context);

        if ($response === false) {
            return ['success' => false, 'message' => 'فشل في الاتصال بشبكة TRON'];
        }

        // التحقق من رمز الاستجابة HTTP
        $http_response_header_line = $http_response_header[0] ?? '';
        if (strpos($http_response_header_line, '404') !== false) {
            return ['success' => false, 'message' => 'معاملة غير موجودة على الشبكة'];
        }

        if (strpos($http_response_header_line, '200') === false) {
            return ['success' => false, 'message' => 'خطأ في الاستعلام عن المعاملة'];
        }
        
        $data = json_decode($response, true);
        
        if (!$data || !isset($data['ret'][0]['contractRet'])) {
            return ['success' => false, 'message' => 'معاملة غير صحيحة أو غير موجودة'];
        }
        
        // التحقق من نجاح المعاملة
        if ($data['ret'][0]['contractRet'] !== 'SUCCESS') {
            return ['success' => false, 'message' => 'المعاملة فشلت على الشبكة'];
        }
        
        // التحقق من نوع المعاملة (USDT Transfer)
        $contract = $data['raw_data']['contract'][0];
        if ($contract['type'] !== 'TriggerSmartContract') {
            return ['success' => false, 'message' => 'نوع المعاملة غير صحيح'];
        }
        
        // التحقق من عنوان العقد (USDT)
        $contractAddress = $contract['parameter']['value']['contract_address'];
        if (bin2hex(base58_decode($contractAddress)) !== USDT_CONTRACT_ADDRESS) {
            return ['success' => false, 'message' => 'عقد العملة غير صحيح'];
        }
        
        // فك تشفير بيانات المعاملة للحصول على المبلغ والعنوان
        $data_hex = $contract['parameter']['value']['data'];
        
        // استخراج عنوان المستقبل (bytes 16-35)
        $to_address_hex = substr($data_hex, 32, 40);
        $to_address = base58_encode(hex2bin('41' . $to_address_hex));
        
        // استخراج المبلغ (bytes 36-67)
        $amount_hex = substr($data_hex, 72, 64);
        $amount = hexdec($amount_hex) / 1000000; // تحويل من أصغر وحدة USDT
        
        // التحقق من العنوان
        if ($to_address !== $toAddress) {
            return ['success' => false, 'message' => 'عنوان المحفظة غير صحيح'];
        }
        
        // التحقق من المبلغ
        if (abs($amount - $expectedAmount) > 0.01) { // هامش خطأ صغير
            return ['success' => false, 'message' => 'المبلغ غير مطابق'];
        }
        
        return [
            'success' => true,
            'message' => 'تم التحقق من المعاملة بنجاح',
            'amount' => $amount,
            'to_address' => $to_address,
            'timestamp' => $data['raw_data']['timestamp']
        ];
        
    } catch (Exception $e) {
        logError("TRON verification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'خطأ في التحقق من المعاملة'];
    }
}

/**
 * دالة لتحويل base58
 */
function base58_decode($input) {
    $alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    $decoded = gmp_init(0);
    $multi = gmp_init(1);
    $s = strrev($input);
    
    for ($i = 0; $i < strlen($s); $i++) {
        $decoded = gmp_add($decoded, gmp_mul($multi, strpos($alphabet, $s[$i])));
        $multi = gmp_mul($multi, 58);
    }
    
    $h = gmp_strval($decoded, 16);
    if (strlen($h) % 2 != 0) {
        $h = '0' . $h;
    }
    
    return $h;
}

function base58_encode($hex) {
    $alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    $decoded = gmp_init($hex, 16);
    $result = '';
    
    while (gmp_cmp($decoded, 0) > 0) {
        list($decoded, $remainder) = gmp_div_qr($decoded, 58);
        $result = $alphabet[gmp_intval($remainder)] . $result;
    }
    
    return $result;
}

/**
 * دالة لتسجيل نشاط المشرف
 */
function logAdminActivity($action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
    if (!isAdminLoggedIn()) return false;
    
    $sql = "INSERT INTO activity_logs (admin_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $_SESSION['admin_id'],
        $action,
        $tableName,
        $recordId,
        $oldValues ? json_encode($oldValues) : null,
        $newValues ? json_encode($newValues) : null,
        $_SERVER['REMOTE_ADDR'] ?? null,
        $_SERVER['HTTP_USER_AGENT'] ?? null
    ];
    
    return insertQuery($sql, $params);
}

/**
 * دالة لتنسيق التاريخ باللغة العربية
 */
function formatArabicDate($timestamp) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $date = date('j', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "{$date} {$month} {$year} - {$time}";
}

/**
 * دالة لتنسيق الأرقام باللغة العربية
 */
function formatArabicNumber($number) {
    $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    return str_replace($western, $arabic, number_format($number));
}

/**
 * دالة لحماية من CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * دالة لرفع الصور
 */
function handleImageUpload($file, $folder = 'general') {
    // التحقق من وجود الملف
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];

    if (!in_array($fileType, $allowedTypes)) {
        return false;
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        return false;
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    $uploadDir = "../assets/uploads/{$folder}/";
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // إنشاء اسم فريد للملف
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = uniqid() . '_' . time() . '.' . $extension;
    $filePath = $uploadDir . $fileName;

    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // إرجاع المسار النسبي للملف
        return "assets/uploads/{$folder}/{$fileName}";
    }

    return false;
}

/**
 * دالة لحذف الصورة
 */
function deleteImage($imagePath) {
    if (empty($imagePath)) {
        return false;
    }

    $fullPath = "../" . $imagePath;

    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }

    return false;
}
?>
