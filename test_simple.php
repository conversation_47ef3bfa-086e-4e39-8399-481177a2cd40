<?php
/**
 * اختبار بسيط لنظام اللغات
 */

// تعريف ثابت للأمان
define('CHARITY_GAZA', true);

// تضمين ملفات النظام
require_once 'includes/config.php';

// بدء الجلسة (إذا لم تكن مبدوءة بالفعل)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معلومات الصفحة
$page_title = 'اختبار بسيط';
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام اللغات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        html {
            direction: <?php echo $text_direction; ?>;
        }
        
        body {
            <?php if ($is_rtl): ?>
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            <?php else: ?>
            font-family: 'Inter', sans-serif;
            direction: ltr;
            text-align: left;
            <?php endif; ?>
            background-color: #111;
            color: #f0f0f0;
        }
        
        .language-selector button {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .language-selector button:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .language-selector .origin-top-right {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            min-width: 200px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .language-selector a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            color: #333;
        }
        
        .language-selector a:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- اللوجو -->
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>">اختبار اللغات</h1>
                </div>

                <!-- منتقي اللغة -->
                <div class="flex items-center">
                    <div class="language-selector-header">
                        <?php echo renderLanguageSelector('header-style'); ?>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="min-h-screen p-8">
        
        <!-- المحتوى -->
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-red-500 mb-8 text-center">
                <?php echo __('site_name'); ?>
            </h1>
            
            <div class="bg-gray-800 p-8 rounded-lg mb-8">
                <h2 class="text-2xl font-bold text-white mb-4">معلومات اللغة الحالية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="bg-gray-700 p-4 rounded">
                        <strong>اللغة:</strong> <?php echo $current_lang; ?>
                    </div>
                    <div class="bg-gray-700 p-4 rounded">
                        <strong>الاتجاه:</strong> <?php echo $text_direction; ?>
                    </div>
                    <div class="bg-gray-700 p-4 rounded">
                        <strong>RTL:</strong> <?php echo $is_rtl ? 'نعم' : 'لا'; ?>
                    </div>
                    <div class="bg-gray-700 p-4 rounded">
                        <strong>اسم الموقع:</strong> <?php echo __('site_name'); ?>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-8 rounded-lg mb-8">
                <h2 class="text-2xl font-bold text-white mb-4">اختبار النصوص</h2>
                <div class="space-y-4">
                    <p><strong>العنوان الرئيسي:</strong> <?php echo __('hero_title'); ?></p>
                    <p><strong>الوصف:</strong> <?php echo __('hero_description'); ?></p>
                    <p><strong>تبرع الآن:</strong> <?php echo __('donate_now'); ?></p>
                    <p><strong>اعرف المزيد:</strong> <?php echo __('learn_more'); ?></p>
                </div>
            </div>
            
            <div class="bg-gray-800 p-8 rounded-lg">
                <h2 class="text-2xl font-bold text-white mb-4">اختبار الاتجاه</h2>
                <div class="space-y-4">
                    <div class="flex items-center <?php echo $is_rtl ? 'justify-end' : 'justify-start'; ?>">
                        <i class="fas fa-arrow-<?php echo $is_rtl ? 'left' : 'right'; ?> text-red-500 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span>هذا النص يجب أن يكون محاذي للـ <?php echo $is_rtl ? 'يمين' : 'يسار'; ?></span>
                    </div>
                    
                    <div class="flex items-center space-x-2 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                        <button class="bg-red-600 px-4 py-2 rounded text-white">زر 1</button>
                        <button class="bg-blue-600 px-4 py-2 rounded text-white">زر 2</button>
                        <button class="bg-green-600 px-4 py-2 rounded text-white">زر 3</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('اللغة الحالية:', '<?php echo $current_lang; ?>');
        console.log('الاتجاه:', '<?php echo $text_direction; ?>');
        console.log('RTL:', <?php echo $is_rtl ? 'true' : 'false'; ?>);
        
        // دالة تبديل منتقي اللغة
        function toggleLanguageMenu() {
            const menu = document.getElementById('language-menu');
            if (menu) {
                menu.classList.toggle('hidden');
                console.log('تم تبديل القائمة');
            } else {
                console.log('لم يتم العثور على القائمة');
            }
        }
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const button = document.getElementById('language-menu-button');
            const menu = document.getElementById('language-menu');
            
            if (button && menu && !button.contains(event.target) && !menu.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });
        
        // اختبار منتقي اللغة - حل محسن
        document.addEventListener('DOMContentLoaded', function() {
            // تأخير قصير للتأكد من تحميل العناصر
            setTimeout(function() {
                const headerButton = document.getElementById('header-language-button');
                const headerMenu = document.getElementById('header-language-menu');

                console.log('زر اللغة:', headerButton);
                console.log('قائمة اللغة:', headerMenu);

                if (headerButton && headerMenu) {
                    console.log('تم العثور على منتقي اللغة');

                    // إضافة مستمع الأحداث
                    headerButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        headerMenu.classList.toggle('hidden');
                        console.log('تم تبديل القائمة');
                    });

                    // إغلاق عند النقر خارج القائمة
                    document.addEventListener('click', function(e) {
                        if (!headerButton.contains(e.target) && !headerMenu.contains(e.target)) {
                            headerMenu.classList.add('hidden');
                        }
                    });

                    // معالجة روابط اللغة
                    const languageLinks = headerMenu.querySelectorAll('a');
                    languageLinks.forEach(link => {
                        link.addEventListener('click', function(e) {
                            console.log('تم اختيار لغة:', this.href);
                            // إظهار رسالة تحميل
                            const loadingDiv = document.createElement('div');
                            loadingDiv.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                            loadingDiv.innerHTML = '<div class="bg-white p-6 rounded-lg text-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div><p class="text-gray-800">جاري تغيير اللغة...</p></div>';
                            document.body.appendChild(loadingDiv);
                        });
                    });
                } else {
                    console.log('لم يتم العثور على منتقي اللغة');
                }
            }, 100);
        });
    </script>
</body>
</html>
