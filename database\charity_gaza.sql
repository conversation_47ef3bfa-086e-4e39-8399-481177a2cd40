-- قاعدة بيانات موقع يداً بيد لأجل غزة
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS charity_gaza CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE charity_gaza;

-- جدول المتبرعين
CREATE TABLE donors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التبرعات
CREATE TABLE donations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    donor_id INT,
    donor_name VARCHAR(255) NOT NULL,
    donor_email VARCHAR(255) NOT NULL,
    package_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    txid VARCHAR(255),
    status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending',
    verification_attempts INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES donors(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_txid (txid),
    INDEX idx_created_at (created_at),
    INDEX idx_donor_email (donor_email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العدادات والإحصائيات
CREATE TABLE counters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    meals_count INT DEFAULT 0,
    families_count INT DEFAULT 0,
    children_count INT DEFAULT 0,
    donors_count INT DEFAULT 0,
    total_donations DECIMAL(12,2) DEFAULT 0.00,
    meals_goal DECIMAL(12,2) DEFAULT 100000.00,
    families_goal DECIMAL(12,2) DEFAULT 50000.00,
    children_goal DECIMAL(12,2) DEFAULT 25000.00,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج القيم الافتراضية للعدادات
INSERT INTO counters (meals_count, families_count, children_count, donors_count) 
VALUES (15420, 8750, 12300, 2840);

-- جدول الشهادات والتوصيات
CREATE TABLE testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج شهادات افتراضية
INSERT INTO testimonials (name, role, message, display_order) VALUES
('أحمد محمد', 'أب لثلاثة أطفال', 'بفضل تبرعاتكم استطعنا إطعام أطفالنا لأسبوع كامل. جزاكم الله خيراً', 1),
('فاطمة أحمد', 'أم لخمسة أطفال', 'الوجبات الساخنة التي وصلتنا أنقذت حياة أطفالي من الجوع. شكراً لكل متبرع', 2),
('محمد عبدالله', 'مسن من غزة', 'في هذا البرد القارس، البطانيات التي وصلتنا كانت نعمة من الله', 3);

-- جدول رسائل التواصل
CREATE TABLE contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مستخدمي لوحة الإدارة
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    role ENUM('admin', 'moderator') DEFAULT 'moderator',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مستخدم إدارة افتراضي (كلمة المرور: admin123)
INSERT INTO admin_users (username, password_hash, full_name, email, role) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');

-- جدول سجل العمليات (للمراجعة)
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT,
    action VARCHAR(255) NOT NULL,
    table_name VARCHAR(100),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإنجازات والمشاريع
CREATE TABLE achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    stats VARCHAR(255),
    image_url VARCHAR(500),
    achievement_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_date (achievement_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إنجازات افتراضية
INSERT INTO achievements (title, description, stats, achievement_date, is_active) VALUES
('توزيع 10,000 وجبة ساخنة', 'تم توزيع 10,000 وجبة ساخنة على العائلات المحتاجة في قطاع غزة خلال شهر ديسمبر', '10,000 وجبة - 2,500 عائلة', '2024-12-15', 1),
('مساعدة 500 عائلة نازحة', 'تقديم المساعدات الأساسية والمأوى المؤقت لـ 500 عائلة نازحة من شمال غزة', '500 عائلة - 2,200 فرد', '2024-12-10', 1),
('توزيع 1,000 بطانية شتوية', 'توزيع البطانيات الشتوية والملابس الدافئة للحماية من البرد القارس', '1,000 بطانية - 800 عائلة', '2024-12-05', 1),
('علاج 200 طفل مصاب', 'تقديم العلاج الطبي والأدوية اللازمة للأطفال المصابين والجرحى', '200 طفل - 50 عملية جراحية', '2024-11-28', 1);
