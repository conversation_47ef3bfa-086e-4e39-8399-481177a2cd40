<?php
/**
 * نظام إدارة اللغات المتعددة
 * Language Management System
 */

class LanguageManager {
    private static $instance = null;
    private $current_language = 'en';
    private $translations = [];
    private $supported_languages = [];
    private $is_admin = false;
    
    private function __construct() {
        global $supported_languages;
        $this->supported_languages = $supported_languages;
        $this->detectLanguage();
        $this->loadTranslations();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحديد اللغة الحالية
     */
    private function detectLanguage() {
        // التحقق من كون المستخدم في لوحة الإدارة
        $this->is_admin = $this->isAdminPanel();
        
        if ($this->is_admin) {
            // لوحة الإدارة تستخدم العربية فقط
            $this->current_language = ADMIN_LANGUAGE;
            return;
        }
        
        // للواجهة الأمامية
        if (isset($_GET['lang']) && $this->isValidLanguage($_GET['lang'])) {
            $this->current_language = $_GET['lang'];
            $_SESSION['language'] = $this->current_language;
        } elseif (isset($_SESSION['language']) && $this->isValidLanguage($_SESSION['language'])) {
            $this->current_language = $_SESSION['language'];
        } else {
            $this->current_language = DEFAULT_LANGUAGE;
        }
    }
    
    /**
     * التحقق من كون المستخدم في لوحة الإدارة
     */
    private function isAdminPanel() {
        $current_path = $_SERVER['REQUEST_URI'];
        return strpos($current_path, '/admin') !== false;
    }
    
    /**
     * التحقق من صحة اللغة
     */
    private function isValidLanguage($lang) {
        return isset($this->supported_languages[$lang]);
    }
    
    /**
     * تحميل ملفات الترجمة
     */
    private function loadTranslations() {
        $language_file = __DIR__ . '/../languages/' . $this->current_language . '.php';
        
        if (file_exists($language_file)) {
            include $language_file;
            if (isset($translations)) {
                $this->translations = $translations;
            }
        } else {
            // تحميل اللغة الافتراضية في حالة عدم وجود الملف
            $default_file = __DIR__ . '/../languages/' . DEFAULT_LANGUAGE . '.php';
            if (file_exists($default_file)) {
                include $default_file;
                if (isset($translations)) {
                    $this->translations = $translations;
                }
            }
        }
    }
    
    /**
     * الحصول على النص المترجم
     */
    public function get($key, $default = null) {
        if (isset($this->translations[$key])) {
            return $this->translations[$key];
        }
        
        return $default ?: $key;
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    public function getCurrentLanguage() {
        return $this->current_language;
    }
    
    /**
     * الحصول على معلومات اللغة الحالية
     */
    public function getCurrentLanguageInfo() {
        return $this->supported_languages[$this->current_language] ?? [];
    }
    
    /**
     * الحصول على جميع اللغات المدعومة
     */
    public function getSupportedLanguages() {
        return $this->supported_languages;
    }
    
    /**
     * الحصول على اتجاه النص للغة الحالية
     */
    public function getDirection() {
        $info = $this->getCurrentLanguageInfo();
        return $info['dir'] ?? 'ltr';
    }
    
    /**
     * التحقق من كون اللغة الحالية RTL
     */
    public function isRTL() {
        return $this->getDirection() === 'rtl';
    }
    
    /**
     * إنشاء رابط مع اللغة
     */
    public function createLanguageUrl($lang, $current_url = null) {
        if ($current_url === null) {
            $current_url = $_SERVER['REQUEST_URI'];
        }
        
        // إزالة معامل اللغة الحالي إن وجد
        $url = preg_replace('/[?&]lang=[^&]*/', '', $current_url);
        
        // إضافة معامل اللغة الجديد
        $separator = strpos($url, '?') !== false ? '&' : '?';
        return $url . $separator . 'lang=' . $lang;
    }
    
    /**
     * عرض منتقي اللغة
     */
    public function renderLanguageSelector($style = '') {
        if ($this->is_admin) {
            return ''; // لا نعرض منتقي اللغة في لوحة الإدارة
        }

        $current_info = $this->getCurrentLanguageInfo();
        $is_rtl = $this->isRTL();

        // تحديد النمط حسب المكان
        if ($style === 'header-style') {
            return $this->renderHeaderStyle($current_info, $is_rtl);
        } elseif ($style === 'mobile-style') {
            return $this->renderMobileStyle($current_info, $is_rtl);
        } else {
            return $this->renderDefaultStyle($current_info, $is_rtl, $style);
        }
    }

    private function renderHeaderStyle($current_info, $is_rtl) {
        $html = '<div class="language-selector-header relative">';
        $html .= '<button type="button" class="flex items-center space-x-2 ' . ($is_rtl ? 'space-x-reverse' : '') . ' bg-gray-800 hover:bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500" id="header-language-button" onclick="toggleHeaderLanguageMenu()">';
        $html .= '<span class="text-lg">' . ($current_info['flag'] ?? '') . '</span>';
        $html .= '<span class="hidden md:inline text-sm font-medium">' . ($current_info['native_name'] ?? $this->current_language) . '</span>';
        $html .= '<span class="md:hidden text-xs font-medium">' . strtoupper($this->current_language) . '</span>';
        $html .= '<svg class="w-4 h-4 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">';
        $html .= '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />';
        $html .= '</svg>';
        $html .= '</button>';

        $html .= '<div class="absolute ' . ($is_rtl ? 'left-0' : 'right-0') . ' mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 hidden z-50" id="header-language-menu">';
        $html .= '<div class="py-2">';

        foreach ($this->supported_languages as $code => $info) {
            $is_current = ($code === $this->current_language);
            $url = $this->createLanguageUrl($code);

            $html .= '<a href="' . $url . '" class="flex items-center px-4 py-3 text-sm hover:bg-gray-50 transition-colors ' . ($is_current ? 'bg-red-50 text-red-700' : 'text-gray-700') . '">';
            $html .= '<span class="text-xl ' . ($is_rtl ? 'ml-3' : 'mr-3') . '">' . $info['flag'] . '</span>';
            $html .= '<div class="flex-1">';
            $html .= '<div class="font-medium">' . $info['native_name'] . '</div>';
            $html .= '<div class="text-xs text-gray-500">' . $info['name'] . '</div>';
            $html .= '</div>';
            if ($is_current) {
                $html .= '<i class="fas fa-check text-red-600 ' . ($is_rtl ? 'mr-2' : 'ml-2') . '"></i>';
            }
            $html .= '</a>';
        }

        $html .= '</div></div></div>';

        // إضافة JavaScript للهيدر
        $html .= '<script>
        function toggleHeaderLanguageMenu() {
            const menu = document.getElementById("header-language-menu");
            if (menu) {
                menu.classList.toggle("hidden");
            }
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener("click", function(event) {
            const button = document.getElementById("header-language-button");
            const menu = document.getElementById("header-language-menu");

            if (button && menu && !button.contains(event.target) && !menu.contains(event.target)) {
                menu.classList.add("hidden");
            }
        });
        </script>';

        return $html;
    }

    private function renderMobileStyle($current_info, $is_rtl) {
        $html = '<div class="language-selector-mobile">';
        $html .= '<button type="button" class="w-full flex items-center justify-between bg-gray-800 hover:bg-gray-700 text-white px-4 py-3 rounded-lg border border-gray-600 transition-all duration-200" id="mobile-language-button" onclick="toggleMobileLanguageMenu()">';
        $html .= '<div class="flex items-center space-x-3 ' . ($is_rtl ? 'space-x-reverse' : '') . '">';
        $html .= '<span class="text-xl">' . ($current_info['flag'] ?? '') . '</span>';
        $html .= '<span class="font-medium">' . ($current_info['native_name'] ?? $this->current_language) . '</span>';
        $html .= '</div>';
        $html .= '<svg class="w-5 h-5 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">';
        $html .= '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />';
        $html .= '</svg>';
        $html .= '</button>';

        $html .= '<div class="mt-2 bg-gray-800 rounded-lg border border-gray-600 hidden" id="mobile-language-menu">';
        $html .= '<div class="py-2">';

        foreach ($this->supported_languages as $code => $info) {
            $is_current = ($code === $this->current_language);
            $url = $this->createLanguageUrl($code);

            $html .= '<a href="' . $url . '" class="flex items-center px-4 py-3 text-sm hover:bg-gray-700 transition-colors ' . ($is_current ? 'bg-red-900 text-red-200' : 'text-gray-300') . '">';
            $html .= '<span class="text-lg ' . ($is_rtl ? 'ml-3' : 'mr-3') . '">' . $info['flag'] . '</span>';
            $html .= '<div class="flex-1">';
            $html .= '<div class="font-medium">' . $info['native_name'] . '</div>';
            $html .= '<div class="text-xs text-gray-400">' . $info['name'] . '</div>';
            $html .= '</div>';
            if ($is_current) {
                $html .= '<i class="fas fa-check text-red-400 ' . ($is_rtl ? 'mr-2' : 'ml-2') . '"></i>';
            }
            $html .= '</a>';
        }

        $html .= '</div></div></div>';
        return $html;
    }

    private function renderDefaultStyle($current_info, $is_rtl, $class) {
        $html = '<div class="language-selector ' . $class . '">';
        $html .= '<div class="relative inline-block text-left">';
        $html .= '<button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" id="default-language-button" aria-expanded="false" aria-haspopup="true" onclick="toggleDefaultLanguageMenu()">';
        $html .= '<span class="' . ($is_rtl ? 'ml-2' : 'mr-2') . '">' . ($current_info['flag'] ?? '') . '</span>';
        $html .= '<span>' . ($current_info['native_name'] ?? $this->current_language) . '</span>';
        $html .= '<svg class="' . ($is_rtl ? 'mr-1 ml-2' : '-mr-1 ml-2') . ' h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">';
        $html .= '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />';
        $html .= '</svg>';
        $html .= '</button>';

        $html .= '<div class="origin-top-right absolute ' . ($is_rtl ? 'left-0' : 'right-0') . ' mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden" role="menu" aria-orientation="vertical" aria-labelledby="default-language-button" id="default-language-menu">';
        $html .= '<div class="py-1" role="none">';

        foreach ($this->supported_languages as $code => $info) {
            $is_current = ($code === $this->current_language);
            $url = $this->createLanguageUrl($code);
            $active = $is_current ? 'bg-gray-100 text-gray-900' : 'text-gray-700';

            $html .= '<a href="' . $url . '" class="' . $active . ' group flex items-center px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900" role="menuitem">';
            $html .= '<span class="' . ($is_rtl ? 'ml-3' : 'mr-3') . '">' . $info['flag'] . '</span>';
            $html .= '<span>' . $info['native_name'] . '</span>';
            if ($is_current) {
                $html .= '<span class="' . ($is_rtl ? 'mr-auto' : 'ml-auto') . '"><i class="fas fa-check text-green-600"></i></span>';
            }
            $html .= '</a>';
        }

        $html .= '</div></div></div></div>';

        // إضافة JavaScript محسن
        $html .= '<script>
        // دالة تبديل قائمة اللغة في الهيدر
        function toggleHeaderLanguageMenu() {
            const menu = document.getElementById("header-language-menu");
            if (menu) {
                menu.classList.toggle("hidden");
                console.log("Header language menu toggled");
            } else {
                console.log("Header language menu not found");
            }
        }

        // دالة تبديل قائمة اللغة للموبايل
        function toggleMobileLanguageMenu() {
            const menu = document.getElementById("mobile-language-menu");
            if (menu) {
                menu.classList.toggle("hidden");
                console.log("Mobile language menu toggled");
            } else {
                console.log("Mobile language menu not found");
            }
        }

        // دالة تبديل قائمة اللغة الافتراضية
        function toggleDefaultLanguageMenu() {
            const menu = document.getElementById("default-language-menu");
            if (menu) {
                menu.classList.toggle("hidden");
                console.log("Default language menu toggled");
            } else {
                console.log("Default language menu not found");
            }
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener("click", function(event) {
            // قائمة اللغة في الهيدر
            const headerButton = document.getElementById("header-language-button");
            const headerMenu = document.getElementById("header-language-menu");

            if (headerButton && headerMenu && !headerButton.contains(event.target) && !headerMenu.contains(event.target)) {
                headerMenu.classList.add("hidden");
            }

            // قائمة اللغة للموبايل
            const mobileButton = document.getElementById("mobile-language-button");
            const mobileMenu = document.getElementById("mobile-language-menu");

            if (mobileButton && mobileMenu && !mobileButton.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.add("hidden");
            }

            // قائمة اللغة الافتراضية
            const defaultButton = document.getElementById("default-language-button");
            const defaultMenu = document.getElementById("default-language-menu");

            if (defaultButton && defaultMenu && !defaultButton.contains(event.target) && !defaultMenu.contains(event.target)) {
                defaultMenu.classList.add("hidden");
            }
        });

        // إضافة رسالة تحميل عند تغيير اللغة
        document.addEventListener("DOMContentLoaded", function() {
            const languageLinks = document.querySelectorAll("#header-language-menu a, #mobile-language-menu a, #default-language-menu a");

            languageLinks.forEach(link => {
                link.addEventListener("click", function(e) {
                    // إظهار رسالة تحميل
                    const loadingDiv = document.createElement("div");
                    loadingDiv.className = "fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center";
                    loadingDiv.innerHTML = `
                        <div class="bg-white p-6 rounded-lg text-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
                            <p class="text-gray-800">جاري تغيير اللغة...</p>
                        </div>
                    `;
                    document.body.appendChild(loadingDiv);
                    console.log("Language change initiated for:", this.href);
                });
            });
        });
        </script>';

        return $html;
    }
}

/**
 * دالة مساعدة للحصول على النص المترجم
 */
function __($key, $default = null) {
    return LanguageManager::getInstance()->get($key, $default);
}

/**
 * دالة مساعدة للحصول على اللغة الحالية
 */
function getCurrentLanguage() {
    return LanguageManager::getInstance()->getCurrentLanguage();
}

/**
 * دالة مساعدة للحصول على اتجاه النص
 */
function getTextDirection() {
    return LanguageManager::getInstance()->getDirection();
}

/**
 * دالة مساعدة للتحقق من RTL
 */
function isRTL() {
    return LanguageManager::getInstance()->isRTL();
}

/**
 * دالة مساعدة لعرض منتقي اللغة
 */
function renderLanguageSelector($class = '') {
    return LanguageManager::getInstance()->renderLanguageSelector($class);
}

// تهيئة نظام اللغات
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إنشاء مثيل من مدير اللغات
$language_manager = LanguageManager::getInstance();
?>
