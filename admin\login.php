<?php
/**
 * صفحة تسجيل دخول المشرف
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// إعادة توجيه إذا كان المشرف مسجل دخول بالفعل
if (isAdminLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error_message = 'اسم المستخدم وكلمة المرور مطلوبان';
    } else {
        // البحث عن المستخدم
        $admin = selectOne(
            "SELECT id, username, password_hash, full_name, email, role, is_active FROM admin_users WHERE username = ? AND is_active = 1",
            [$username]
        );
        
        if ($admin && verifyPassword($password, $admin['password_hash'])) {
            // تسجيل دخول ناجح
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['full_name'];
            $_SESSION['admin_role'] = $admin['role'];
            
            // تحديث وقت آخر دخول
            updateQuery(
                "UPDATE admin_users SET last_login = NOW() WHERE id = ?",
                [$admin['id']]
            );
            
            // تسجيل النشاط
            logAdminActivity('تسجيل دخول', 'admin_users', $admin['id']);
            
            header('Location: index.php');
            exit;
        } else {
            $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة إدارة <?php echo SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/favicon.png">
    <link rel="apple-touch-icon" href="../assets/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            min-height: 100vh;
        }
        
        .login-container {
            background: rgba(31, 41, 55, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #374151;
        }
        
        .input-field {
            background-color: #111827;
            border: 1px solid #374151;
            color: #f9fafb;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            border-color: #e63946;
            box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
            outline: none;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #e63946 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(230, 57, 70, 0.3);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left-color: #ef4444;
            color: #fca5a5;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left-color: #10b981;
            color: #6ee7b7;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="login-container w-full max-w-md p-8 rounded-xl shadow-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-shield text-3xl text-white"></i>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">لوحة الإدارة</h1>
            <p class="text-gray-400"><?php echo SITE_NAME; ?></p>
        </div>
        
        <!-- Error/Success Messages -->
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Login Form -->
        <form method="POST" action="" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-user mr-2"></i>اسم المستخدم
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="input-field w-full px-4 py-3 rounded-lg"
                    placeholder="أدخل اسم المستخدم"
                    required
                    autocomplete="username"
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                >
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-lock mr-2"></i>كلمة المرور
                </label>
                <div class="relative">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="input-field w-full px-4 py-3 rounded-lg pr-12"
                        placeholder="أدخل كلمة المرور"
                        required
                        autocomplete="current-password"
                    >
                    <button 
                        type="button" 
                        onclick="togglePassword()" 
                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                    >
                        <i id="passwordIcon" class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-600 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800">
                    <span class="mr-2 text-sm text-gray-300">تذكرني</span>
                </label>
                <a href="#" class="text-sm text-red-400 hover:text-red-300">نسيت كلمة المرور؟</a>
            </div>
            
            <button 
                type="submit" 
                class="btn-login w-full py-3 px-4 rounded-lg text-white font-bold text-lg"
            >
                <i class="fas fa-sign-in-alt mr-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <!-- Footer -->
        <div class="mt-8 text-center">
            <p class="text-gray-400 text-sm">
                © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>
            </p>
            <a href="../index.php" class="text-red-400 hover:text-red-300 text-sm">
                <i class="fas fa-arrow-right mr-1"></i>العودة للموقع الرئيسي
            </a>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
            }
        }
        
        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // Handle form submission with loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري تسجيل الدخول...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
