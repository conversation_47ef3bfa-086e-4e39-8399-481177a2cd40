<?php
/**
 * صفحة إدارة الشهادات
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_testimonial':
                $name = sanitizeInput($_POST['name']);
                $role = sanitizeInput($_POST['role']);
                $message_text = sanitizeInput($_POST['message']);
                $testimonial_date = sanitizeInput($_POST['testimonial_date'] ?? date('Y-m-d'));

                if (!empty($name) && !empty($role) && !empty($message_text)) {
                    $testimonial_id = insertQuery(
                        "INSERT INTO testimonials (name, role, message, testimonial_date) VALUES (?, ?, ?, ?)",
                        [$name, $role, $message_text, $testimonial_date]
                    );
                    
                    if ($testimonial_id) {
                        logAdminActivity('إضافة شهادة جديدة', 'testimonials', $testimonial_id);
                        $message = 'تم إضافة الشهادة بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء إضافة الشهادة';
                        $message_type = 'error';
                    }
                } else {
                    $message = 'جميع الحقول مطلوبة';
                    $message_type = 'error';
                }
                break;
                
            case 'update_testimonial':
                $testimonial_id = intval($_POST['testimonial_id']);
                $name = sanitizeInput($_POST['name']);
                $role = sanitizeInput($_POST['role']);
                $message_text = sanitizeInput($_POST['message']);
                $testimonial_date = sanitizeInput($_POST['testimonial_date'] ?? date('Y-m-d'));
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                $old_testimonial = selectOne("SELECT * FROM testimonials WHERE id = ?", [$testimonial_id]);

                updateQuery(
                    "UPDATE testimonials SET name = ?, role = ?, message = ?, testimonial_date = ?, is_active = ? WHERE id = ?",
                    [$name, $role, $message_text, $testimonial_date, $is_active, $testimonial_id]
                );
                
                logAdminActivity('تحديث شهادة', 'testimonials', $testimonial_id, $old_testimonial);
                $message = 'تم تحديث الشهادة بنجاح';
                $message_type = 'success';
                break;
                
            case 'toggle_status':
                $testimonial_id = intval($_POST['testimonial_id']);
                $testimonial = selectOne("SELECT * FROM testimonials WHERE id = ?", [$testimonial_id]);
                
                if ($testimonial) {
                    $new_status = $testimonial['is_active'] ? 0 : 1;
                    updateQuery("UPDATE testimonials SET is_active = ? WHERE id = ?", [$new_status, $testimonial_id]);
                    
                    logAdminActivity('تغيير حالة شهادة', 'testimonials', $testimonial_id);
                    $message = $new_status ? 'تم تفعيل الشهادة' : 'تم إلغاء تفعيل الشهادة';
                    $message_type = 'success';
                }
                break;
                
            case 'delete_testimonial':
                $testimonial_id = intval($_POST['testimonial_id']);
                $testimonial = selectOne("SELECT * FROM testimonials WHERE id = ?", [$testimonial_id]);
                
                deleteQuery("DELETE FROM testimonials WHERE id = ?", [$testimonial_id]);
                
                logAdminActivity('حذف شهادة', 'testimonials', $testimonial_id, $testimonial);
                $message = 'تم حذف الشهادة بنجاح';
                $message_type = 'success';
                break;
        }
    }
}

// فلترة البيانات
$status_filter = $_GET['status'] ?? 'all';
$search = sanitizeInput($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = [];
$params = [];

if ($status_filter === 'active') {
    $where_conditions[] = "is_active = 1";
} elseif ($status_filter === 'inactive') {
    $where_conditions[] = "is_active = 0";
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR role LIKE ? OR message LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// جلب الشهادات مرتبة حسب تاريخ الشهادة (الأحدث أولاً)
$testimonials = selectQuery(
    "SELECT t.*, DATE_FORMAT(t.created_at, '%Y-%m-%d %H:%i') as formatted_date
     FROM testimonials t
     {$where_clause}
     ORDER BY t.testimonial_date DESC, t.created_at DESC
     LIMIT {$per_page} OFFSET {$offset}",
    $params
);

// عدد الشهادات الإجمالي
$total_testimonials = selectOne(
    "SELECT COUNT(*) as count FROM testimonials t {$where_clause}",
    $params
)['count'] ?? 0;

$total_pages = ceil($total_testimonials / $per_page);

// إحصائيات سريعة
$stats = [
    'total' => selectOne("SELECT COUNT(*) as count FROM testimonials")['count'] ?? 0,
    'active' => selectOne("SELECT COUNT(*) as count FROM testimonials WHERE is_active = 1")['count'] ?? 0,
    'inactive' => selectOne("SELECT COUNT(*) as count FROM testimonials WHERE is_active = 0")['count'] ?? 0
];

$page_title = 'إدارة الشهادات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid #334155;
        }
        
        .table-container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
        }
        
        .nav-link {
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background-color: rgba(230, 57, 70, 0.1);
            border-right: 3px solid #e63946;
        }
        
        .nav-link.active {
            background-color: rgba(230, 57, 70, 0.2);
            border-right: 3px solid #e63946;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            background-color: #1e293b;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-slate-900">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">لوحة الإدارة</h1>
                        <p class="text-sm text-gray-400"><?php echo SITE_NAME; ?></p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="index.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="mr-3">الرئيسية</span>
                    </a>
                    <a href="donations.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-hand-holding-heart w-5"></i>
                        <span class="mr-3">التبرعات</span>
                    </a>
                    <a href="contacts.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="mr-3">الرسائل</span>
                    </a>
                    <a href="testimonials.php" class="nav-link active flex items-center px-4 py-3 text-white rounded-lg">
                        <i class="fas fa-quote-right w-5"></i>
                        <span class="mr-3">الشهادات</span>
                    </a>
                    <a href="achievements.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-trophy w-5"></i>
                        <span class="mr-3">الإنجازات</span>
                    </a>
                    <a href="counters.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span class="mr-3">العدادات</span>
                    </a>
                </nav>
            </div>
            
            <!-- User Info -->
            <div class="absolute bottom-0 w-full p-6 border-t border-gray-700">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></p>
                        <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['admin_role']); ?></p>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-400" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-slate-800 border-b border-slate-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-white">إدارة الشهادات</h1>
                        <p class="text-gray-400">إضافة وإدارة شهادات المستفيدين</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button onclick="openAddModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-plus mr-2"></i>إضافة شهادة جديدة
                        </button>
                        <a href="../index.php" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>عرض الموقع
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <main class="p-6">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-600' : 'bg-red-600'; ?> text-white">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-white"><?php echo number_format($stats['total']); ?></p>
                            <p class="text-gray-400 text-sm">إجمالي الشهادات</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-400"><?php echo number_format($stats['active']); ?></p>
                            <p class="text-gray-400 text-sm">نشطة</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-gray-400"><?php echo number_format($stats['inactive']); ?></p>
                            <p class="text-gray-400 text-sm">غير نشطة</p>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="table-container rounded-xl p-6 mb-6">
                    <form method="GET" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input 
                                type="text" 
                                name="search" 
                                placeholder="البحث بالاسم، الدور، أو الرسالة..." 
                                value="<?php echo htmlspecialchars($search); ?>"
                                class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500"
                            >
                        </div>
                        <div>
                            <select name="status" class="px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الشهادات</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشطة</option>
                                <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشطة</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-search mr-2"></i>بحث
                        </button>
                        <a href="testimonials.php" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-times mr-2"></i>إعادة تعيين
                        </a>
                    </form>
                </div>
                
                <!-- Testimonials Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php if (!empty($testimonials)): ?>
                        <?php foreach ($testimonials as $testimonial): ?>
                            <div class="table-container rounded-xl p-6 <?php echo !$testimonial['is_active'] ? 'opacity-60' : ''; ?>">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                            <?php echo mb_substr($testimonial['name'], 0, 1); ?>
                                        </div>
                                        <div class="mr-3">
                                            <h4 class="font-bold text-white"><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                            <p class="text-gray-400 text-sm"><?php echo htmlspecialchars($testimonial['role']); ?></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <?php if ($testimonial['is_active']): ?>
                                            <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">نشط</span>
                                        <?php else: ?>
                                            <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        <?php endif; ?>
                                        <span class="text-gray-400 text-xs"><?php echo $testimonial['testimonial_date'] ? date('Y/m/d', strtotime($testimonial['testimonial_date'])) : 'غير محدد'; ?></span>
                                    </div>
                                </div>
                                
                                <p class="text-gray-300 leading-relaxed mb-4">
                                    "<?php echo htmlspecialchars($testimonial['message']); ?>"
                                </p>
                                
                                <div class="flex items-center justify-between text-xs text-gray-400 mb-4">
                                    <span><?php echo $testimonial['formatted_date']; ?></span>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                
                                <div class="flex gap-2">
                                    <button onclick="editTestimonial(<?php echo htmlspecialchars(json_encode($testimonial)); ?>)" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm">
                                        <i class="fas fa-edit mr-1"></i>تعديل
                                    </button>
                                    
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="testimonial_id" value="<?php echo $testimonial['id']; ?>">
                                        <button type="submit" class="bg-<?php echo $testimonial['is_active'] ? 'yellow' : 'green'; ?>-600 hover:bg-<?php echo $testimonial['is_active'] ? 'yellow' : 'green'; ?>-700 text-white px-3 py-2 rounded text-sm">
                                            <i class="fas fa-<?php echo $testimonial['is_active'] ? 'pause' : 'play'; ?> mr-1"></i>
                                            <?php echo $testimonial['is_active'] ? 'إيقاف' : 'تفعيل'; ?>
                                        </button>
                                    </form>
                                    
                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الشهادة؟')">
                                        <input type="hidden" name="action" value="delete_testimonial">
                                        <input type="hidden" name="testimonial_id" value="<?php echo $testimonial['id']; ?>">
                                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm">
                                            <i class="fas fa-trash mr-1"></i>حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-span-full table-container rounded-xl p-12 text-center">
                            <i class="fas fa-quote-right text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-400">لا توجد شهادات مطابقة للبحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Add/Edit Modal -->
    <div id="testimonialModal" class="modal">
        <div class="modal-content">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-white" id="modalTitle">إضافة شهادة جديدة</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" id="testimonialForm">
                <input type="hidden" name="action" id="formAction" value="add_testimonial">
                <input type="hidden" name="testimonial_id" id="testimonialId">
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">الاسم الكامل</label>
                    <input type="text" name="name" id="testimonialName" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" required>
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">الدور أو الصفة</label>
                    <input type="text" name="role" id="testimonialRole" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" placeholder="مثال: أب لثلاثة أطفال" required>
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">نص الشهادة</label>
                    <textarea name="message" id="testimonialMessage" rows="4" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white resize-none focus:outline-none focus:border-red-500" placeholder="اكتب نص الشهادة هنا..." required></textarea>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">تاريخ الشهادة</label>
                    <input type="date" name="testimonial_date" id="testimonialDate" value="<?php echo date('Y-m-d'); ?>" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500">
                </div>
                
                <div class="mb-6" id="statusCheckbox" style="display: none;">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" id="testimonialActive" class="rounded border-gray-600 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800">
                        <span class="mr-2 text-gray-300">نشط (يظهر في الموقع)</span>
                    </label>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        <span id="submitText">إضافة الشهادة</span>
                    </button>
                    <button type="button" onclick="closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة شهادة جديدة';
            document.getElementById('formAction').value = 'add_testimonial';
            document.getElementById('submitText').textContent = 'إضافة الشهادة';
            document.getElementById('statusCheckbox').style.display = 'none';
            document.getElementById('testimonialForm').reset();
            document.getElementById('testimonialModal').style.display = 'block';
        }
        
        function editTestimonial(testimonial) {
            document.getElementById('modalTitle').textContent = 'تعديل الشهادة';
            document.getElementById('formAction').value = 'update_testimonial';
            document.getElementById('submitText').textContent = 'حفظ التغييرات';
            document.getElementById('statusCheckbox').style.display = 'block';
            
            document.getElementById('testimonialId').value = testimonial.id;
            document.getElementById('testimonialName').value = testimonial.name;
            document.getElementById('testimonialRole').value = testimonial.role;
            document.getElementById('testimonialMessage').value = testimonial.message;
            document.getElementById('testimonialDate').value = testimonial.testimonial_date || '';
            document.getElementById('testimonialActive').checked = testimonial.is_active == 1;
            
            document.getElementById('testimonialModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('testimonialModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('testimonialModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
