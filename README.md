# موقع يداً بيد لأجل غزة - نظام التبرعات الخيرية

## وصف المشروع
موقع خيري رقمي احترافي لجمع التبرعات لأهالي غزة مع نظام تحقق من المعاملات عبر شبكة TRON.

## متطلبات التشغيل
- PHP 8.1 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- XAMPP أو LAMP للتطوير المحلي
- إضافة GMP في PHP (للتحقق من TRON)

## هيكل المشروع
```
charity_gaza/
├── index.php               # الصفحة الرئيسية
├── admin/                  # لوحة تحكم المشرف
│   ├── index.php          # الرئيسية
│   ├── login.php          # تسجيل الدخول
│   ├── logout.php         # تسجيل الخروج
│   └── donations.php      # إدارة التبرعات
├── includes/               # ملفات الوظائف والاتصال
│   ├── config.php         # الإعدادات
│   ├── db.php             # الاتصال بقاعدة البيانات
│   ├── functions.php      # الوظائف المشتركة
│   ├── header.php         # الهيدر المشترك
│   └── footer.php         # الفوتر المشترك
├── assets/                 # الصور والستايلات والسكريبتات
│   ├── css/
│   │   └── style.css      # ملف CSS الرئيسي
│   ├── js/
│   │   └── main.js        # ملف JavaScript الرئيسي
│   └── images/            # الصور
├── database/               # ملفات قاعدة البيانات
│   └── charity_gaza.sql   # ملف إنشاء قاعدة البيانات
├── logs/                   # ملفات السجلات
└── README.md              # هذا الملف
```

## خطوات التثبيت

### 1. إعداد قاعدة البيانات
```sql
-- أنشئ قاعدة بيانات جديدة
CREATE DATABASE charity_gaza CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استورد ملف قاعدة البيانات
mysql -u root -p charity_gaza < database/charity_gaza.sql
```

### 2. تحديث إعدادات الاتصال
عدّل ملف `includes/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'charity_gaza');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 3. إعداد البريد الإلكتروني
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### 4. إعداد محفظة TRON
```php
define('TRON_WALLET_ADDRESS', 'TZ3CpHsxDbfzMEicU5BPSe2YSEEbaF6XeF');
```

## الجداول المطلوبة
- `donors`: بيانات المتبرعين
- `donations`: سجل التبرعات مع TXID
- `counters`: العدادات والإحصائيات
- `testimonials`: الشهادات والتوصيات
- `contacts`: رسائل التواصل
- `admin_users`: مستخدمي لوحة الإدارة
- `activity_logs`: سجل أنشطة المشرفين

## بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: غيّر كلمة المرور فوراً بعد أول تسجيل دخول!

## المزايا المطلوبة المنجزة ✅

### ✅ المرحلة 1: إعداد البيئة والمجلدات
- [x] هيكل المجلدات المطلوب
- [x] ملفات الإعدادات الأساسية

### ✅ المرحلة 2: إعداد قاعدة البيانات
- [x] قاعدة البيانات charity_gaza
- [x] جميع الجداول المطلوبة مع العلاقات
- [x] البيانات الافتراضية

### ✅ المرحلة 3: نظام التبرعات
- [x] أزرار التبرع تفتح نوافذ Modal فعالة
- [x] استخراج نوع التبرع وقيمة التبرع
- [x] طلب بيانات المتبرع (الاسم، البريد، المبلغ، TXID)
- [x] التحقق من صحة الحقول برمجياً
- [x] تخزين البيانات في جدول donations
- [x] إرسال رسالة تأكيد مبدئية

### ✅ المرحلة 4: التحقق من TXID على TRON
- [x] استخدام Tron API للتحقق من المعاملات
- [x] التحقق من المبلغ والعنوان والوقت
- [x] تحديث status إلى "مؤكد" عند النجاح
- [x] إرسال تأكيد نهائي بالبريد
- [x] زر "تحقق" في لوحة الإدارة

### ✅ المرحلة 5: العدادات المتحركة
- [x] عدادات متحركة (meals, families, children, donors)
- [x] جلب القيم من جدول counters
- [x] تحريك تدريجي باستخدام JavaScript

### ✅ المرحلة 6: نظام الإدارة Admin
- [x] صفحة تسجيل دخول آمنة (login.php)
- [x] نظام الجلسات (PHP Sessions)
- [x] لوحة تحكم شاملة
- [x] قسم التبرعات (عرض، تصفية، تأكيد، حذف)
- [x] قسم الشهادات (إضافة، عرض، تعديل، حذف)
- [x] قسم الإحصائيات
- [x] قسم التواصل (عرض الرسائل)

### ✅ المزايا الإضافية
- [x] ملف functions.php للدوال الشائعة
- [x] إرسال البريد الإلكتروني
- [x] التحقق من المعاملة عبر Tron
- [x] التحقق من الجلسة والصلاحيات
- [x] نظام توجيه بسيط
- [x] قالب header.php و footer.php
- [x] تصميم متسق باستخدام Tailwind CSS

## طريقة التشغيل
1. انسخ مجلد `charity_gaza` إلى مجلد htdocs في XAMPP
2. شغّل خدمات Apache و MySQL من لوحة تحكم XAMPP
3. أنشئ قاعدة البيانات واستورد ملف `database/charity_gaza.sql`
4. افتح المتصفح على: `http://localhost/charity_gaza`
5. للوحة الإدارة: `http://localhost/charity_gaza/admin`

## الاختبار
- ✅ إرسال تبرع وتأكيده عبر TXID
- ✅ عرض العدادات المتحركة
- ✅ استخدام نموذج التواصل
- ✅ الدخول إلى لوحة المشرف واستخدامها
- ✅ إدارة التبرعات والشهادات

## الأمان
- تشفير كلمات المرور
- حماية من SQL Injection
- حماية من XSS
- نظام CSRF Token
- تسجيل أنشطة المشرفين
- حماية مجلد logs

## معلومات الاتصال
- البريد الإلكتروني: <EMAIL>
- الهاتف: +970 599 123 456
- محفظة TRON: TZ3CpHsxDbfzMEicU5BPSe2YSEEbaF6XeF

## الترخيص
هذا المشروع مفتوح المصدر لأغراض خيرية فقط.

---

## ملاحظات مهمة للتطوير

### إعداد البريد الإلكتروني
لتفعيل إرسال البريد الإلكتروني، تحتاج إلى:
1. إنشاء App Password في Gmail
2. تحديث إعدادات SMTP في config.php
3. أو استخدام مكتبة PHPMailer للمزيد من الخيارات

### تحسين الأداء
- استخدم Redis أو Memcached للتخزين المؤقت
- فعّل ضغط GZIP
- استخدم CDN للملفات الثابتة

### النسخ الاحتياطي
- اعمل نسخة احتياطية من قاعدة البيانات يومياً
- احفظ نسخة من ملفات الموقع

### المراقبة
- راقب ملفات السجلات في مجلد logs/
- تابع أداء قاعدة البيانات
- راقب استخدام الخادم
