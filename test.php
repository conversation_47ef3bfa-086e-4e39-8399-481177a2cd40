<?php
echo "<h1>اختبار PHP</h1>";
echo "<p>PHP يعمل بشكل صحيح!</p>";
echo "<p>إصدار PHP: " . PHP_VERSION . "</p>";

// اختبار الاتصال بـ MySQL
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color: green;'>✅ MySQL متصل</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS charity_gaza");
    echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات</p>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=charity_gaza", "root", "");
    
    // إنشاء جدول بسيط
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))");
    echo "<p style='color: green;'>✅ تم إنشاء جدول اختبار</p>";
    
    echo "<h2>الآن جرب الموقع:</h2>";
    echo "<a href='index.php'>اذهب للموقع الرئيسي</a>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
