/**
 * JavaScript لدعم اللغات المتعددة
 * Multi-language Support JavaScript
 */

// متغيرات عامة
let currentLanguage = 'en';
let isRTL = false;
let translations = {};

// تهيئة نظام اللغات
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguageSystem();
    initializeLanguageSelector();
    initializeDirectionSupport();
    initializeCounters();
    forceDirectionUpdate();
});

/**
 * تهيئة نظام اللغات
 */
function initializeLanguageSystem() {
    // الحصول على اللغة الحالية من HTML
    currentLanguage = document.documentElement.lang || 'en';
    isRTL = document.documentElement.dir === 'rtl';
    
    // تطبيق الإعدادات حسب اللغة
    applyLanguageSettings();
}

/**
 * تهيئة منتقي اللغة
 */
function initializeLanguageSelector() {
    const languageButton = document.getElementById('language-menu-button');
    const languageMenu = document.getElementById('language-menu');
    
    if (languageButton && languageMenu) {
        // إظهار/إخفاء القائمة
        languageButton.addEventListener('click', function(e) {
            e.stopPropagation();
            languageMenu.classList.toggle('hidden');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!languageButton.contains(e.target) && !languageMenu.contains(e.target)) {
                languageMenu.classList.add('hidden');
            }
        });
        
        // إغلاق القائمة عند اختيار لغة
        languageMenu.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                languageMenu.classList.add('hidden');
                showLanguageChangeNotification();
            }
        });
    }
}

/**
 * تهيئة دعم الاتجاهات
 */
function initializeDirectionSupport() {
    // تطبيق فئات CSS حسب الاتجاه
    const body = document.body;

    if (isRTL) {
        body.classList.add('rtl-layout');
        body.classList.remove('ltr-layout');
    } else {
        body.classList.add('ltr-layout');
        body.classList.remove('rtl-layout');
    }

    // تحديث أيقونات الاتجاه
    updateDirectionIcons();
}

/**
 * فرض تحديث الاتجاه
 */
function forceDirectionUpdate() {
    const html = document.documentElement;
    const body = document.body;

    // التأكد من تطبيق الاتجاه على HTML
    if (isRTL) {
        html.setAttribute('dir', 'rtl');
        body.style.direction = 'rtl';
        body.style.textAlign = 'right';
    } else {
        html.setAttribute('dir', 'ltr');
        body.style.direction = 'ltr';
        body.style.textAlign = 'left';
    }

    // تطبيق الاتجاه على جميع العناصر الرئيسية
    const mainElements = document.querySelectorAll('main, section, div, p, h1, h2, h3, h4, h5, h6');
    mainElements.forEach(element => {
        if (!element.hasAttribute('data-no-direction')) {
            element.style.direction = isRTL ? 'rtl' : 'ltr';
        }
    });
}

/**
 * تطبيق إعدادات اللغة
 */
function applyLanguageSettings() {
    // تحديث اتجاه النص في العناصر الديناميكية
    const dynamicElements = document.querySelectorAll('.dynamic-text');
    dynamicElements.forEach(element => {
        element.dir = isRTL ? 'rtl' : 'ltr';
    });
    
    // تحديث تنسيق الأرقام
    updateNumberFormatting();
    
    // تحديث تنسيق التواريخ
    updateDateFormatting();
}

/**
 * تحديث أيقونات الاتجاه
 */
function updateDirectionIcons() {
    const icons = document.querySelectorAll('.direction-icon');
    icons.forEach(icon => {
        if (isRTL) {
            // عكس الأيقونات للغات RTL
            if (icon.classList.contains('fa-arrow-right')) {
                icon.classList.remove('fa-arrow-right');
                icon.classList.add('fa-arrow-left');
            } else if (icon.classList.contains('fa-chevron-right')) {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
            }
        }
    });
}

/**
 * تحديث تنسيق الأرقام
 */
function updateNumberFormatting() {
    const numberElements = document.querySelectorAll('.format-number');
    numberElements.forEach(element => {
        const number = parseFloat(element.textContent);
        if (!isNaN(number)) {
            if (currentLanguage === 'ar') {
                // تحويل الأرقام للعربية
                element.textContent = convertToArabicNumbers(number.toLocaleString());
            } else {
                element.textContent = number.toLocaleString(currentLanguage);
            }
        }
    });
}

/**
 * تحديث تنسيق التواريخ
 */
function updateDateFormatting() {
    const dateElements = document.querySelectorAll('.format-date');
    dateElements.forEach(element => {
        const dateStr = element.getAttribute('data-date');
        if (dateStr) {
            const date = new Date(dateStr);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            
            if (currentLanguage === 'ar') {
                element.textContent = formatArabicDate(date);
            } else {
                element.textContent = date.toLocaleDateString(currentLanguage, options);
            }
        }
    });
}

/**
 * تحويل الأرقام للعربية
 */
function convertToArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.replace(/[0-9]/g, function(match) {
        return arabicNumbers[parseInt(match)];
    });
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatArabicDate(date) {
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    
    return `${convertToArabicNumbers(day.toString())} ${month} ${convertToArabicNumbers(year.toString())}`;
}

/**
 * تهيئة العدادات المتحركة
 */
function initializeCounters() {
    const counters = document.querySelectorAll('[id$="Counter"]');
    
    // قيم العدادات
    const counterValues = {
        mealsCounter: 15420,
        familiesCounter: 8750,
        sheltersCounter: 3200,
        medicalCounter: 5680
    };
    
    // تشغيل العدادات عند ظهورها
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const targetValue = counterValues[counter.id] || 0;
                animateCounter(counter, targetValue);
                observer.unobserve(counter);
            }
        });
    });
    
    counters.forEach(counter => observer.observe(counter));
}

/**
 * تحريك العداد
 */
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const duration = 2000; // 2 ثانية
    const stepTime = duration / 100;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        const displayValue = Math.floor(current);
        if (currentLanguage === 'ar') {
            element.textContent = convertToArabicNumbers(displayValue.toLocaleString());
        } else {
            element.textContent = displayValue.toLocaleString(currentLanguage);
        }
    }, stepTime);
}

/**
 * إظهار إشعار تغيير اللغة
 */
function showLanguageChangeNotification() {
    const messages = {
        'en': 'Language changed successfully',
        'ar': 'تم تغيير اللغة بنجاح',
        'ru': 'Язык успешно изменен',
        'de': 'Sprache erfolgreich geändert',
        'sv': 'Språk ändrat framgångsrikt',
        'zh': '语言更改成功',
        'no': 'Språk endret vellykket',
        'pt': 'Idioma alterado com sucesso',
        'tr': 'Dil başarıyla değiştirildi',
        'es': 'Idioma cambiado exitosamente',
        'id': 'Bahasa berhasil diubah',
        'ja': '言語が正常に変更されました',
        'ms': 'Bahasa berjaya ditukar'
    };
    
    const message = messages[currentLanguage] || messages['en'];
    showNotification(message, 'success');
}

/**
 * إظهار الإشعارات
 */
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * نسخ النص للحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        const messages = {
            'en': 'Copied to clipboard',
            'ar': 'تم النسخ للحافظة',
            'ru': 'Скопировано в буфер обмена',
            'de': 'In die Zwischenablage kopiert',
            'sv': 'Kopierat till urklipp',
            'zh': '已复制到剪贴板',
            'no': 'Kopiert til utklippstavle',
            'pt': 'Copiado para a área de transferência',
            'tr': 'Panoya kopyalandı',
            'es': 'Copiado al portapapeles',
            'id': 'Disalin ke clipboard',
            'ja': 'クリップボードにコピーされました',
            'ms': 'Disalin ke papan keratan'
        };
        
        const message = messages[currentLanguage] || messages['en'];
        showNotification(message, 'success');
    }).catch(() => {
        const messages = {
            'en': 'Failed to copy',
            'ar': 'فشل في النسخ',
            'ru': 'Не удалось скопировать',
            'de': 'Kopieren fehlgeschlagen',
            'sv': 'Misslyckades att kopiera',
            'zh': '复制失败',
            'no': 'Kunne ikke kopiere',
            'pt': 'Falha ao copiar',
            'tr': 'Kopyalama başarısız',
            'es': 'Error al copiar',
            'id': 'Gagal menyalin',
            'ja': 'コピーに失敗しました',
            'ms': 'Gagal menyalin'
        };
        
        const message = messages[currentLanguage] || messages['en'];
        showNotification(message, 'error');
    });
}

/**
 * تحديث محتوى الصفحة ديناميكياً
 */
function updatePageContent() {
    // تحديث العناوين
    updatePageTitles();
    
    // تحديث النصوص
    updatePageTexts();
    
    // تحديث النماذج
    updateForms();
}

/**
 * تحديث العناوين
 */
function updatePageTitles() {
    const titleElements = document.querySelectorAll('[data-translate]');
    titleElements.forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[key]) {
            element.textContent = translations[key];
        }
    });
}

/**
 * تحديث النصوص
 */
function updatePageTexts() {
    // تحديث النصوص القابلة للترجمة
    const textElements = document.querySelectorAll('.translatable');
    textElements.forEach(element => {
        const key = element.getAttribute('data-key');
        if (key && translations[key]) {
            element.textContent = translations[key];
        }
    });
}

/**
 * تحديث النماذج
 */
function updateForms() {
    // تحديث placeholders
    const inputs = document.querySelectorAll('input[data-placeholder], textarea[data-placeholder]');
    inputs.forEach(input => {
        const key = input.getAttribute('data-placeholder');
        if (key && translations[key]) {
            input.placeholder = translations[key];
        }
    });
    
    // تحديث تسميات الحقول
    const labels = document.querySelectorAll('label[data-label]');
    labels.forEach(label => {
        const key = label.getAttribute('data-label');
        if (key && translations[key]) {
            label.textContent = translations[key];
        }
    });
}

// تصدير الدوال للاستخدام العام
window.LanguageManager = {
    showNotification,
    copyToClipboard,
    updatePageContent,
    getCurrentLanguage: () => currentLanguage,
    isRTL: () => isRTL
};
