<?php
/**
 * الصفحة الرئيسية للوحة الإدارة
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

// جلب الإحصائيات
$stats = [
    'total_donations' => selectOne("SELECT COUNT(*) as count FROM donations")['count'] ?? 0,
    'confirmed_donations' => selectOne("SELECT COUNT(*) as count FROM donations WHERE status = 'confirmed'")['count'] ?? 0,
    'pending_donations' => selectOne("SELECT COUNT(*) as count FROM donations WHERE status = 'pending'")['count'] ?? 0,
    'total_amount' => selectOne("SELECT SUM(amount) as total FROM donations WHERE status = 'confirmed'")['total'] ?? 0,
    'total_contacts' => selectOne("SELECT COUNT(*) as count FROM contacts")['count'] ?? 0,
    'unread_contacts' => selectOne("SELECT COUNT(*) as count FROM contacts WHERE is_read = 0")['count'] ?? 0,
    'total_testimonials' => selectOne("SELECT COUNT(*) as count FROM testimonials")['count'] ?? 0,
    'active_testimonials' => selectOne("SELECT COUNT(*) as count FROM testimonials WHERE is_active = 1")['count'] ?? 0,
    'total_achievements' => selectOne("SELECT COUNT(*) as count FROM achievements")['count'] ?? 0,
    'active_achievements' => selectOne("SELECT COUNT(*) as count FROM achievements WHERE is_active = 1")['count'] ?? 0
];

// جلب آخر التبرعات
$recent_donations = selectQuery(
    "SELECT d.*, DATE_FORMAT(d.created_at, '%Y-%m-%d %H:%i') as formatted_date 
     FROM donations d 
     ORDER BY d.created_at DESC 
     LIMIT 10"
);

// جلب آخر الرسائل
$recent_contacts = selectQuery(
    "SELECT c.*, DATE_FORMAT(c.created_at, '%Y-%m-%d %H:%i') as formatted_date 
     FROM contacts c 
     ORDER BY c.created_at DESC 
     LIMIT 5"
);

$page_title = 'لوحة الإدارة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/favicon.png">
    <link rel="apple-touch-icon" href="../assets/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid #334155;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border-color: #e63946;
        }
        
        .table-container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
        }
        
        .nav-link {
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background-color: rgba(230, 57, 70, 0.1);
            border-right: 3px solid #e63946;
        }
        
        .nav-link.active {
            background-color: rgba(230, 57, 70, 0.2);
            border-right: 3px solid #e63946;
        }
    </style>
</head>
<body class="bg-slate-900">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">لوحة الإدارة</h1>
                        <p class="text-sm text-gray-400"><?php echo SITE_NAME; ?></p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="index.php" class="nav-link active flex items-center px-4 py-3 text-white rounded-lg">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="mr-3">الرئيسية</span>
                    </a>
                    <a href="donations.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-hand-holding-heart w-5"></i>
                        <span class="mr-3">التبرعات</span>
                        <?php if ($stats['pending_donations'] > 0): ?>
                            <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full mr-auto">
                                <?php echo $stats['pending_donations']; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="contacts.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="mr-3">الرسائل</span>
                        <?php if ($stats['unread_contacts'] > 0): ?>
                            <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full mr-auto">
                                <?php echo $stats['unread_contacts']; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="testimonials.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-quote-right w-5"></i>
                        <span class="mr-3">الشهادات</span>
                    </a>
                    <a href="achievements.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-trophy w-5"></i>
                        <span class="mr-3">الإنجازات</span>
                    </a>
                    <a href="counters.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span class="mr-3">العدادات</span>
                    </a>
                    <a href="settings.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-cog w-5"></i>
                        <span class="mr-3">الإعدادات</span>
                    </a>
                </nav>
            </div>
            
            <!-- User Info -->
            <div class="absolute bottom-0 w-full p-6 border-t border-gray-700">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></p>
                        <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['admin_role']); ?></p>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-400" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-slate-800 border-b border-slate-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-white">مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?></h1>
                        <p class="text-gray-400">إليك نظرة عامة على أنشطة الموقع</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="../index.php" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>عرض الموقع
                        </a>
                        <div class="text-gray-400 text-sm">
                            <?php echo formatArabicDate(time()); ?>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <main class="p-6">
                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-hand-holding-heart text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">إجمالي التبرعات</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format($stats['total_donations']); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">التبرعات المؤكدة</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format($stats['confirmed_donations']); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">في الانتظار</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format($stats['pending_donations']); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">المبلغ الإجمالي</p>
                                <p class="text-2xl font-bold text-white">$<?php echo number_format($stats['total_amount'], 2); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-trophy text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">الإنجازات</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format($stats['active_achievements']); ?></p>
                                <p class="text-xs text-gray-500">من أصل <?php echo $stats['total_achievements']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Donations -->
                    <div class="table-container rounded-xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-bold text-white">آخر التبرعات</h2>
                            <a href="donations.php" class="text-red-400 hover:text-red-300 text-sm">
                                عرض الكل <i class="fas fa-arrow-left mr-1"></i>
                            </a>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-700">
                                        <th class="text-right py-3 text-gray-400 font-medium">المتبرع</th>
                                        <th class="text-right py-3 text-gray-400 font-medium">المبلغ</th>
                                        <th class="text-right py-3 text-gray-400 font-medium">الحالة</th>
                                        <th class="text-right py-3 text-gray-400 font-medium">التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($recent_donations)): ?>
                                        <?php foreach ($recent_donations as $donation): ?>
                                            <tr class="border-b border-gray-800">
                                                <td class="py-3 text-white"><?php echo htmlspecialchars($donation['donor_name']); ?></td>
                                                <td class="py-3 text-white">$<?php echo number_format($donation['amount'], 2); ?></td>
                                                <td class="py-3">
                                                    <?php if ($donation['status'] === 'confirmed'): ?>
                                                        <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">مؤكد</span>
                                                    <?php elseif ($donation['status'] === 'pending'): ?>
                                                        <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs">معلق</span>
                                                    <?php else: ?>
                                                        <span class="bg-red-600 text-white px-2 py-1 rounded text-xs">مرفوض</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="py-3 text-gray-400 text-sm"><?php echo $donation['formatted_date']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="py-8 text-center text-gray-400">لا توجد تبرعات حتى الآن</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Recent Messages -->
                    <div class="table-container rounded-xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-bold text-white">آخر الرسائل</h2>
                            <a href="contacts.php" class="text-red-400 hover:text-red-300 text-sm">
                                عرض الكل <i class="fas fa-arrow-left mr-1"></i>
                            </a>
                        </div>
                        
                        <div class="space-y-4">
                            <?php if (!empty($recent_contacts)): ?>
                                <?php foreach ($recent_contacts as $contact): ?>
                                    <div class="border-b border-gray-800 pb-4">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class="font-medium text-white"><?php echo htmlspecialchars($contact['name']); ?></h4>
                                                <p class="text-gray-400 text-sm"><?php echo htmlspecialchars($contact['email']); ?></p>
                                                <p class="text-gray-300 text-sm mt-1"><?php echo mb_substr(htmlspecialchars($contact['message']), 0, 100) . '...'; ?></p>
                                            </div>
                                            <div class="text-left">
                                                <?php if (!$contact['is_read']): ?>
                                                    <span class="bg-red-600 w-3 h-3 rounded-full inline-block"></span>
                                                <?php endif; ?>
                                                <p class="text-gray-400 text-xs mt-1"><?php echo $contact['formatted_date']; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-center text-gray-400 py-8">لا توجد رسائل حتى الآن</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
