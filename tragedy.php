<?php
/**
 * صفحة المأساة - عرض الوضع الإنساني في غزة
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// معلومات الصفحة
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

$page_title = __('tragedy_title');
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #111;
            color: #f0f0f0;
        }
        .tragedy-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), 
                        url('https://images.unsplash.com/photo-1632495288245-81b9d20c0cdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                    <i class="fas fa-heart text-white text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"><?php echo __('site_name'); ?></h1>
            </div>
            <nav class="hidden md:block">
                <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                    <li><a href="index.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_home'); ?></a></li>
                    <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_kitchen'); ?></a></li>
                    <li><a href="tragedy.php" class="text-red-500 font-medium"><?php echo __('nav_tragedy'); ?></a></li>
                    <li><a href="donate.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_donate'); ?></a></li>
                    <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_testimonials'); ?></a></li>
                    <li><a href="contact.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_contact'); ?></a></li>
                </ul>
            </nav>

            <!-- Language Selector -->
            <div class="flex items-center">
                <div class="language-selector-header">
                    <?php echo renderLanguageSelector('header-style'); ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="tragedy-bg text-white py-32">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-heart-broken text-6xl text-red-500 crying-icon"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('tragedy_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('tragedy_description_1'); ?></p>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('shocking_facts'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('tragedy_description_2'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center p-6 bg-red-900 bg-opacity-20 rounded-lg border border-red-800">
                    <i class="fas fa-users text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">2.3M</div>
                    <div class="text-gray-300"><?php echo __('stats_families'); ?></div>
                </div>
                <div class="text-center p-6 bg-red-900 bg-opacity-20 rounded-lg border border-red-800">
                    <i class="fas fa-home text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">500K</div>
                    <div class="text-gray-300"><?php echo __('stats_shelters'); ?></div>
                </div>
                <div class="text-center p-6 bg-red-900 bg-opacity-20 rounded-lg border border-red-800">
                    <i class="fas fa-child text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">1M</div>
                    <div class="text-gray-300"><?php echo __('fact_1'); ?></div>
                </div>
                <div class="text-center p-6 bg-red-900 bg-opacity-20 rounded-lg border border-red-800">
                    <i class="fas fa-hospital text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">70%</div>
                    <div class="text-gray-300"><?php echo __('fact_3'); ?></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Crisis Details -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-red-500 mb-6"><?php echo __('tragedy_subtitle'); ?></h2>
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl mt-1 <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>"></i>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_meal'); ?></h3>
                                <p class="text-gray-300"><?php echo __('tragedy_description_1'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-tint text-red-500 text-xl mt-1 <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>"></i>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-2"><?php echo __('fact_2'); ?></h3>
                                <p class="text-gray-300"><?php echo __('tragedy_description_2'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-bolt text-red-500 text-xl mt-1 <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>"></i>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-2"><?php echo __('emergency_appeal'); ?></h3>
                                <p class="text-gray-300"><?php echo __('urgent_help'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-user-md text-red-500 text-xl mt-1 <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>"></i>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_medical'); ?></h3>
                                <p class="text-gray-300"><?php echo __('fact_3'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-gray-700 p-8 rounded-lg">
                        <i class="fas fa-heart-broken text-6xl text-red-500 mb-6"></i>
                        <h3 class="text-2xl font-bold text-white mb-4"><?php echo __('urgent_help'); ?></h3>
                        <p class="text-gray-300 mb-6"><?php echo __('hero_description'); ?></p>
                        <a href="donate.php" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg">
                            <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('donate_now'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stories Section -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('testimonials_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('testimonials_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-quote-right text-red-500 text-2xl <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                        <div>
                            <h3 class="text-white font-bold"><?php echo __('testimonial_2_name'); ?></h3>
                            <p class="text-gray-400 text-sm"><?php echo __('testimonial_2_role'); ?></p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic">"<?php echo __('testimonial_2_message'); ?>"</p>
                </div>

                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-quote-right text-red-500 text-2xl <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                        <div>
                            <h3 class="text-white font-bold"><?php echo __('testimonial_1_name'); ?></h3>
                            <p class="text-gray-400 text-sm"><?php echo __('testimonial_1_role'); ?></p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic">"<?php echo __('testimonial_1_message'); ?>"</p>
                </div>

                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-quote-right text-red-500 text-2xl <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                        <div>
                            <h3 class="text-white font-bold"><?php echo __('testimonial_3_name'); ?></h3>
                            <p class="text-gray-400 text-sm"><?php echo __('testimonial_3_role'); ?></p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic">"<?php echo __('testimonial_3_message'); ?>"</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Winter Crisis -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('winter_campaign'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('package_blanket_desc'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="bg-blue-900 bg-opacity-30 p-8 rounded-lg border border-blue-800">
                        <i class="fas fa-snowflake text-4xl text-blue-400 mb-4"></i>
                        <h3 class="text-2xl font-bold text-white mb-4"><?php echo __('winter_campaign'); ?></h3>
                        <ul class="space-y-3 text-gray-300">
                            <li class="flex items-center">
                                <i class="fas fa-thermometer-empty text-blue-400 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                                <?php echo __('package_blanket'); ?>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-home text-blue-400 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                                <?php echo __('package_shelter'); ?>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-tshirt text-blue-400 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                                <?php echo __('package_clothes'); ?>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-bed text-blue-400 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"></i>
                                <?php echo __('package_blanket_desc'); ?>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-6"><?php echo __('kitchen_cta_title'); ?></h3>
                    <p class="text-gray-300 mb-8"><?php echo __('kitchen_cta_desc'); ?></p>
                    <div class="grid grid-cols-2 gap-4">
                        <a href="donate.php?package=blanket" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg">
                            <i class="fas fa-bed <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('package_blanket'); ?> - $20
                        </a>
                        <a href="donate.php?package=clothes" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg">
                            <i class="fas fa-tshirt <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('package_clothes'); ?> - $30
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-red-900">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-white mb-4"><?php echo __('emergency_appeal'); ?></h2>
            <p class="text-xl text-gray-200 mb-8"><?php echo __('hero_description'); ?></p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="donate.php" class="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-lg text-lg">
                    <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('donate_now'); ?>
                </a>
                <a href="charity_kitchen.php" class="border-2 border-white text-white hover:bg-white hover:text-red-900 font-bold py-4 px-8 rounded-lg text-lg">
                    <i class="fas fa-utensils <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('kitchen_achievements_title'); ?>
                </a>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
