<?php
/**
 * صفحة التواصل
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'submit_contact':
            $response = processContact($_POST);
            echo json_encode($response);
            exit;
    }
}

// دالة معالجة رسائل التواصل (نسخة من index.php)
function processContact($data) {
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['email']) || empty($data['message'])) {
        return ['success' => false, 'message' => 'جميع الحقول مطلوبة'];
    }
    
    if (!validateEmail($data['email'])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
    }
    
    // تنظيف البيانات
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $subject = sanitizeInput($data['subject'] ?? 'رسالة عامة');
    $message = sanitizeInput($data['message']);
    
    // إدراج الرسالة
    $contactId = insertQuery(
        "INSERT INTO contacts (name, email, subject, message, created_at) 
         VALUES (?, ?, ?, ?, NOW())",
        [$name, $email, $subject, $message]
    );
    
    if ($contactId) {
        return ['success' => true, 'message' => 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً'];
    } else {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إرسال الرسالة'];
    }
}

// معلومات الصفحة
$page_title = __('contact_title');
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #111;
            color: #f0f0f0;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- اللوجو -->
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>">
                        <a href="index.php"><?php echo __('site_name'); ?></a>
                    </h1>
                </div>

                <!-- التنقل الرئيسي -->
                <nav class="hidden lg:block">
                    <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                        <li><a href="index.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_home'); ?></a></li>
                        <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_kitchen'); ?></a></li>
                        <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_tragedy'); ?></a></li>
                        <li><a href="donate.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_donate'); ?></a></li>
                        <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_testimonials'); ?></a></li>
                        <li><a href="contact.php" class="text-red-500 font-medium"><?php echo __('nav_contact'); ?></a></li>
                    </ul>
                </nav>

                <!-- منتقي اللغة -->
                <div class="flex items-center">
                    <?php echo renderLanguageSelector(); ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-red-900 to-red-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-envelope text-6xl text-yellow-400"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('contact_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('contact_subtitle'); ?></p>
        </div>
    </section>

    <!-- Contact Info -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-envelope text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('footer_email'); ?></h3>
                    <p class="text-gray-300"><?php echo FROM_EMAIL; ?></p>
                    <p class="text-gray-300"><EMAIL></p>
                </div>
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-phone text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('footer_phone'); ?></h3>
                    <p class="text-gray-300"><?php echo __('phone_number'); ?></p>
                </div>
                <div class="text-center p-6 bg-gray-800 rounded-lg">
                    <i class="fas fa-clock text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2"><?php echo __('working_hours'); ?></h3>
                    <p class="text-gray-300"><?php echo __('working_days'); ?></p>
                    <p class="text-gray-300"><?php echo __('working_time'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('send_message'); ?></h2>
                    <p class="text-gray-300"><?php echo __('contact_description'); ?></p>
                </div>

                <div class="bg-gray-900 p-8 rounded-lg border border-gray-700">
                    <form id="contactForm">
                        <input type="hidden" name="action" value="submit_contact">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-white font-medium mb-2"><?php echo __('full_name'); ?> *</label>
                                <input type="text" name="name" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-800 text-white" required>
                            </div>
                            <div>
                                <label class="block text-white font-medium mb-2"><?php echo __('email_address'); ?> *</label>
                                <input type="email" name="email" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-800 text-white" required>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-white font-medium mb-2"><?php echo __('subject'); ?></label>
                            <select name="subject" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-800 text-white">
                                <option value="general_inquiry"><?php echo __('subject_general_inquiry'); ?></option>
                                <option value="donation_receipt"><?php echo __('subject_donation_receipt'); ?></option>
                                <option value="volunteer"><?php echo __('subject_volunteer'); ?></option>
                                <option value="complaint"><?php echo __('subject_complaint'); ?></option>
                                <option value="other"><?php echo __('subject_other'); ?></option>
                            </select>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-white font-medium mb-2"><?php echo __('your_message'); ?> *</label>
                            <textarea name="message" rows="6" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-800 text-white" placeholder="<?php echo __('message_placeholder'); ?>" required></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-lg">
                            <span class="btn-text">
                                <i class="fas fa-paper-plane <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('send_message'); ?>
                            </span>
                            <span class="btn-loading hidden">
                                <i class="fas fa-spinner fa-spin <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('sending'); ?>
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('faq_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('faq_subtitle'); ?></p>
            </div>

            <div class="max-w-3xl mx-auto space-y-6">
                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <button class="w-full p-6 <?php echo $is_rtl ? 'text-right' : 'text-left'; ?> focus:outline-none" onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <i class="fas fa-chevron-down text-red-500 transform transition-transform"></i>
                            <h3 class="text-white font-bold"><?php echo __('faq_how_to_donate'); ?></h3>
                        </div>
                    </button>
                    <div class="faq-content hidden p-6 pt-0">
                        <p class="text-gray-300"><?php echo __('faq_how_to_donate_answer'); ?></p>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <button class="w-full p-6 <?php echo $is_rtl ? 'text-right' : 'text-left'; ?> focus:outline-none" onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <i class="fas fa-chevron-down text-red-500 transform transition-transform"></i>
                            <h3 class="text-white font-bold"><?php echo __('faq_is_secure'); ?></h3>
                        </div>
                    </button>
                    <div class="faq-content hidden p-6 pt-0">
                        <p class="text-gray-300"><?php echo __('faq_is_secure_answer'); ?></p>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <button class="w-full p-6 <?php echo $is_rtl ? 'text-right' : 'text-left'; ?> focus:outline-none" onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <i class="fas fa-chevron-down text-red-500 transform transition-transform"></i>
                            <h3 class="text-white font-bold"><?php echo __('faq_how_long'); ?></h3>
                        </div>
                    </button>
                    <div class="faq-content hidden p-6 pt-0">
                        <p class="text-gray-300"><?php echo __('faq_how_long_answer'); ?></p>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <button class="w-full p-6 <?php echo $is_rtl ? 'text-right' : 'text-left'; ?> focus:outline-none" onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <i class="fas fa-chevron-down text-red-500 transform transition-transform"></i>
                            <h3 class="text-white font-bold"><?php echo __('faq_volunteer'); ?></h3>
                        </div>
                    </button>
                    <div class="faq-content hidden p-6 pt-0">
                        <p class="text-gray-300"><?php echo __('faq_volunteer_answer'); ?></p>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <button class="w-full p-6 <?php echo $is_rtl ? 'text-right' : 'text-left'; ?> focus:outline-none" onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <i class="fas fa-chevron-down text-red-500 transform transition-transform"></i>
                            <h3 class="text-white font-bold"><?php echo __('faq_distribution'); ?></h3>
                        </div>
                    </button>
                    <div class="faq-content hidden p-6 pt-0">
                        <p class="text-gray-300"><?php echo __('faq_distribution_answer'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Media -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-red-500 mb-8"><?php echo __('footer_follow'); ?></h2>
            <div class="flex justify-center space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                <a href="#" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full text-2xl">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="bg-blue-400 hover:bg-blue-500 text-white p-4 rounded-full text-2xl">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="bg-pink-600 hover:bg-pink-700 text-white p-4 rounded-full text-2xl">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-full text-2xl">
                    <i class="fab fa-youtube"></i>
                </a>
                <a href="#" class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-full text-2xl">
                    <i class="fab fa-whatsapp"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Notification Container -->
    <div id="notificationContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <script>
        // معالجة نموذج التواصل
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            const formData = new FormData(form);
            
            // التحقق من صحة البيانات
            const name = formData.get('name').trim();
            const email = formData.get('email').trim();
            const message = formData.get('message').trim();
            
            if (!name || !email || !message) {
                showNotification('جميع الحقول المطلوبة يجب ملؤها', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showNotification('البريد الإلكتروني غير صحيح', 'error');
                return;
            }
            
            // تغيير حالة الزر
            setButtonLoading(submitBtn, true);
            
            // إرسال البيانات
            fetch('contact.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                setButtonLoading(submitBtn, false);
                
                if (data.success) {
                    showNotification(data.message, 'success');
                    form.reset();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                setButtonLoading(submitBtn, false);
                showNotification('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.', 'error');
            });
        });
        
        // دالة لتبديل الأسئلة الشائعة
        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
