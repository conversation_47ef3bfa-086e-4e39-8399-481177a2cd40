<?php
/**
 * ملف الإعدادات الرئيسي لموقع يداً بيد لأجل غزة
 */

// منع الوصول المباشر
if (!defined('CHARITY_GAZA')) {
    die('Access denied');
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'charity_gaza');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الموقع
define('SITE_NAME', 'يداً بيد لأجل غزة');
define('SITE_URL', 'http://localhost/gaza/charity_gaza');
define('ADMIN_URL', SITE_URL . '/admin');
define('ASSETS_URL', SITE_URL . '/assets');

// إعدادات اللغات
define('DEFAULT_LANGUAGE', 'en'); // الإنجليزية كلغة افتراضية
define('ADMIN_LANGUAGE', 'ar'); // العربية للوحة الإدارة فقط

// اللغات المدعومة
$supported_languages = [
    'en' => [
        'name' => 'English',
        'native_name' => 'English',
        'flag' => '🇺🇸',
        'dir' => 'ltr',
        'default' => true
    ],
    'ar' => [
        'name' => 'Arabic',
        'native_name' => 'العربية',
        'flag' => '🇸🇦',
        'dir' => 'rtl'
    ],
    'ru' => [
        'name' => 'Russian',
        'native_name' => 'Русский',
        'flag' => '🇷🇺',
        'dir' => 'ltr'
    ],
    'de' => [
        'name' => 'German',
        'native_name' => 'Deutsch',
        'flag' => '🇩🇪',
        'dir' => 'ltr'
    ],
    'sv' => [
        'name' => 'Swedish',
        'native_name' => 'Svenska',
        'flag' => '🇸🇪',
        'dir' => 'ltr'
    ],
    'zh' => [
        'name' => 'Chinese',
        'native_name' => '中文',
        'flag' => '🇨🇳',
        'dir' => 'ltr'
    ],
    'no' => [
        'name' => 'Norwegian',
        'native_name' => 'Norsk',
        'flag' => '🇳🇴',
        'dir' => 'ltr'
    ],
    'pt' => [
        'name' => 'Portuguese',
        'native_name' => 'Português',
        'flag' => '🇧🇷',
        'dir' => 'ltr'
    ],
    'tr' => [
        'name' => 'Turkish',
        'native_name' => 'Türkçe',
        'flag' => '🇹🇷',
        'dir' => 'ltr'
    ],
    'es' => [
        'name' => 'Spanish',
        'native_name' => 'Español',
        'flag' => '🇪🇸',
        'dir' => 'ltr'
    ],
    'id' => [
        'name' => 'Indonesian',
        'native_name' => 'Bahasa Indonesia',
        'flag' => '🇮🇩',
        'dir' => 'ltr'
    ],
    'ja' => [
        'name' => 'Japanese',
        'native_name' => '日本語',
        'flag' => '🇯🇵',
        'dir' => 'ltr'
    ],
    'ms' => [
        'name' => 'Malaysian',
        'native_name' => 'Bahasa Malaysia',
        'flag' => '🇲🇾',
        'dir' => 'ltr'
    ]
];

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'يداً بيد لأجل غزة');

// إعدادات TRON API
define('TRON_API_URL', 'https://api.trongrid.io');
define('TRON_WALLET_ADDRESS', 'TZ3CpHsxDbfzMEicU5BPSe2YSEEbaF6XeF');
define('USDT_CONTRACT_ADDRESS', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t');

// إعدادات الأمان
define('SESSION_LIFETIME', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات التبرعات
define('MIN_DONATION_AMOUNT', 1);
define('MAX_DONATION_AMOUNT', 100000);

// أنواع التبرعات المتاحة
$donation_packages = [
    'meal' => [
        'name' => 'وجبة ساخنة',
        'amount' => 5,
        'description' => 'وجبة ساخنة لعائلة مكونة من 5 أفراد'
    ],
    'family_aid' => [
        'name' => 'مساعدة عائلة',
        'amount' => 50,
        'description' => 'مساعدة شهرية لعائلة محتاجة'
    ],
    'shelter' => [
        'name' => 'مأوى آمن',
        'amount' => 100,
        'description' => 'توفير مأوى آمن لعائلة مشردة'
    ],
    'medical' => [
        'name' => 'مساعدة طبية',
        'amount' => 75,
        'description' => 'توفير الأدوية والعلاج الطبي'
    ],
    'blanket' => [
        'name' => 'بطانية شتوية',
        'amount' => 20,
        'description' => 'بطانية شتوية لحماية من البرد'
    ],
    'clothes' => [
        'name' => 'ملابس شتوية',
        'amount' => 30,
        'description' => 'كيس ملابس شتوية لطفل'
    ]
];

// أهداف التبرعات
$donation_goals = [
    'meals' => 100000,      // هدف الوجبات
    'families' => 50000,    // هدف مساعدة العائلات
    'shelter' => 25000,     // هدف المأوى
    'medical' => 30000,     // هدف المساعدات الطبية
    'winter' => 15000       // هدف المساعدات الشتوية
];

// رسائل النظام
$system_messages = [
    'donation_success' => 'تم إرسال طلب التبرع بنجاح. سيتم التحقق من المعاملة خلال 24 ساعة.',
    'donation_error' => 'حدث خطأ أثناء معالجة طلب التبرع. يرجى المحاولة مرة أخرى.',
    'contact_success' => 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.',
    'contact_error' => 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.',
    'login_success' => 'تم تسجيل الدخول بنجاح.',
    'login_error' => 'اسم المستخدم أو كلمة المرور غير صحيحة.',
    'access_denied' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
    'session_expired' => 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.'
];

// إعدادات التحقق من TXID
define('TXID_VERIFICATION_TIMEOUT', 30); // ثانية
define('MAX_VERIFICATION_ATTEMPTS', 3);

// إعدادات الملفات المرفوعة
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);

// تفعيل عرض الأخطاء في بيئة التطوير
if ($_SERVER['HTTP_HOST'] === 'localhost' || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false) {
    define('DEBUG_MODE', true);
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    define('DEBUG_MODE', false);
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Gaza');

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS

// تشفير البيانات الحساسة
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('ENCRYPTION_METHOD', 'AES-256-CBC');

// تضمين نظام إدارة اللغات
require_once __DIR__ . '/language_manager.php';
?>
