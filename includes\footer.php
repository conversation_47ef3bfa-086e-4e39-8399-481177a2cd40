<?php
/**
 * ملف الفوتر المشترك
 */

// منع الوصول المباشر
if (!defined('CHARITY_GAZA')) {
    die('Access denied');
}
?>

    <!-- Footer -->
    <footer class="bg-black text-white py-12 border-t border-red-900">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 text-red-400"><?php echo __('site_name'); ?></h3>
                    <p class="text-gray-400">
                        <?php echo __('footer_about_text'); ?>
                    </p>
                    <div class="mt-4">
                        <h4 class="font-bold text-white mb-2"><?php echo __('footer_follow'); ?>:</h4>
                        <div class="flex space-x-4 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                            <a href="#" class="text-gray-400 hover:text-red-400 text-xl">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-red-400 text-xl">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-red-400 text-xl">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-red-400 text-xl">
                                <i class="fab fa-telegram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4 text-red-400"><?php echo __('footer_quick_links'); ?></h3>
                    <ul class="space-y-2">
                        <li><a href="index.php" class="text-gray-400 hover:text-red-400"><?php echo __('nav_home'); ?></a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-red-400"><?php echo __('about_title'); ?></a></li>
                        <li><a href="#donate" class="text-gray-400 hover:text-red-400"><?php echo __('nav_donate'); ?></a></li>
                        <li><a href="#testimonials" class="text-gray-400 hover:text-red-400"><?php echo __('nav_testimonials'); ?></a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-red-400"><?php echo __('nav_contact'); ?></a></li>
                        <li><a href="privacy.php" class="text-gray-400 hover:text-red-400"><?php echo __('privacy_policy'); ?></a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4 text-red-400"><?php echo __('contact_info'); ?></h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <i class="fas fa-map-marker-alt text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('address_label'); ?></h4>
                                <p class="text-gray-400"><?php echo __('address_text'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-phone-alt text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('emergency_phone'); ?></h4>
                                <p class="text-gray-400"><?php echo __('phone_number'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope text-red-400 mt-1 <?php echo $is_rtl ? 'ml-3' : 'mr-3'; ?>"></i>
                            <div>
                                <h4 class="font-bold text-white"><?php echo __('email_receipts'); ?></h4>
                                <p class="text-gray-400"><?php echo FROM_EMAIL; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 text-sm mb-4 md:mb-0">
                        © <?php echo date('Y'); ?> <?php echo __('site_name'); ?>. <?php echo __('footer_rights'); ?>.
                    </div>
                    <div class="text-gray-400 text-sm">
                        <span><?php echo __('donation_wallet'); ?>: </span>
                        <span class="text-green-400 font-mono"><?php echo TRON_WALLET_ADDRESS; ?></span>
                        <button onclick="copyToClipboard('<?php echo TRON_WALLET_ADDRESS; ?>')" class="<?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?> text-red-400 hover:text-red-300">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Donation Modal -->
    <div id="donationModal" class="modal">
        <div class="modal-content">
            <div class="bg-red-600 text-white p-4 rounded-t-lg">
                <span class="close">&times;</span>
                <h2 class="text-xl font-bold">تبرع الآن لإنقاذ الأرواح</h2>
            </div>
            
            <div class="p-6">
                <!-- Wallet Address Display -->
                <div class="mb-6 text-center">
                    <h3 class="text-lg font-bold text-white mb-2">عنوان محفظة التبرعات</h3>
                    <div class="bg-gray-800 p-3 rounded text-green-400 font-mono text-sm overflow-x-auto mb-2" id="walletAddress"><?php echo TRON_WALLET_ADDRESS; ?></div>
                    <button onclick="copyToClipboard('<?php echo TRON_WALLET_ADDRESS; ?>')" class="w-full bg-gray-800 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm">
                        <i class="fas fa-copy mr-1"></i> نسخ عنوان المحفظة
                    </button>
                    <p class="text-red-400 text-sm mt-2"><i class="fas fa-exclamation-circle mr-1"></i> تأكد من إرسال التبرع على شبكة TRC20 فقط</p>
                </div>
                
                <!-- Donation Form -->
                <form id="donationForm" class="space-y-4">
                    <input type="hidden" name="action" value="submit_donation">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div>
                        <label for="donationPackage" class="block text-white font-medium mb-2">نوع التبرع</label>
                        <input type="text" id="donationPackage" name="package" readonly class="w-full p-3 border border-gray-700 rounded-lg bg-gray-900 text-red-400 font-bold">
                    </div>
                    
                    <div>
                        <label for="donationAmount" class="block text-white font-medium mb-2">مبلغ التبرع (USDT)</label>
                        <input type="number" id="donationAmount" name="amount" step="0.01" min="<?php echo MIN_DONATION_AMOUNT; ?>" max="<?php echo MAX_DONATION_AMOUNT; ?>" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>
                    
                    <div>
                        <label for="donorName" class="block text-white font-medium mb-2">الاسم الكامل</label>
                        <input type="text" id="donorName" name="name" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>
                    
                    <div>
                        <label for="donorEmail" class="block text-white font-medium mb-2">البريد الإلكتروني</label>
                        <input type="email" id="donorEmail" name="email" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>
                    
                    <div>
                        <label for="txid" class="block text-white font-medium mb-2">معرف التحويل (TXID) - اختياري</label>
                        <input type="text" id="txid" name="txid" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" placeholder="أدخل الـ TXID بعد الإرسال">
                        <p class="text-gray-400 text-sm mt-1">يمكنك إضافة معرف التحويل لاحقاً عبر صفحة الاتصال</p>
                    </div>
                    
                    <div class="flex gap-4">
                        <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg donate-btn">
                            <span class="btn-text">إرسال طلب التبرع</span>
                            <span class="btn-loading hidden">
                                <span class="loading"></span> جاري الإرسال...
                            </span>
                        </button>
                        <button type="button" onclick="closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- JavaScript Files -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/multilang.js"></script>
    
    <script>
        // Global variables
        const SITE_URL = '<?php echo SITE_URL; ?>';
        const ASSETS_URL = '<?php echo ASSETS_URL; ?>';
        const CSRF_TOKEN = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeCounters();
            initializeDonationButtons();
            initializeMobileMenu();
            initializeContactForm();
        });
    </script>
</body>
</html>
