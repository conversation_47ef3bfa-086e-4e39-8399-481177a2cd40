<?php
/**
 * صفحة التبرعات المخصصة
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// معالجة المعاملات المرسلة من الصفحة الرئيسية
$selected_package = isset($_GET['package']) ? sanitizeInput($_GET['package']) : '';
$selected_amount = isset($_GET['amount']) ? floatval($_GET['amount']) : 0;

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'submit_donation':
            $response = processDonation($_POST);
            echo json_encode($response);
            exit;
    }
}

// دالة معالجة التبرعات (نسخة من index.php)
function processDonation($data) {
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['email']) || empty($data['amount']) || empty($data['package'])) {
        return ['success' => false, 'message' => 'جميع الحقول مطلوبة'];
    }
    
    if (!validateEmail($data['email'])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
    }
    
    $amount = floatval($data['amount']);
    if ($amount < MIN_DONATION_AMOUNT || $amount > MAX_DONATION_AMOUNT) {
        return ['success' => false, 'message' => 'مبلغ التبرع غير صحيح'];
    }
    
    // تنظيف البيانات
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $package = sanitizeInput($data['package']);
    $txid = !empty($data['txid']) ? sanitizeInput($data['txid']) : null;
    
    // إدراج التبرع مباشرة
    $donationId = insertQuery(
        "INSERT INTO donations (donor_name, donor_email, package_type, amount, txid, status, created_at) 
         VALUES (?, ?, ?, ?, ?, 'pending', NOW())",
        [$name, $email, $package, $amount, $txid]
    );
    
    if ($donationId) {
        return ['success' => true, 'message' => 'تم إرسال طلب التبرع بنجاح'];
    } else {
        return ['success' => false, 'message' => 'حدث خطأ أثناء معالجة التبرع'];
    }
}

// معلومات الصفحة
$page_title = __('nav_donate');
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

// استخدام القيم المحددة مسبقاً أو القيم الافتراضية
if (empty($selected_package)) {
    $selected_package = 'meal';
}
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #111;
            color: #f0f0f0;
        }
        .donation-package {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .donation-package:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(230, 57, 70, 0.2);
        }
        .donation-package.selected {
            border-color: #e63946;
            background-color: rgba(230, 57, 70, 0.1);
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- اللوجو -->
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>">
                        <a href="index.php"><?php echo __('site_name'); ?></a>
                    </h1>
                </div>

                <!-- التنقل الرئيسي -->
                <nav class="hidden lg:block">
                    <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                        <li><a href="index.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_home'); ?></a></li>
                        <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_kitchen'); ?></a></li>
                        <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_tragedy'); ?></a></li>
                        <li><a href="donate.php" class="text-red-500 font-medium"><?php echo __('nav_donate'); ?></a></li>
                        <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_testimonials'); ?></a></li>
                        <li><a href="contact.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_contact'); ?></a></li>
                    </ul>
                </nav>

                <!-- منتقي اللغة -->
                <div class="flex items-center">
                    <?php echo renderLanguageSelector(); ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-red-900 to-red-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-hand-holding-heart text-6xl text-yellow-400"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('donate_now'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('hero_description'); ?></p>
        </div>
    </section>

    <!-- Donation Packages -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('donation_packages'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('hero_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- وجبة ساخنة -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="meal" data-amount="5" data-name="<?php echo __('package_meal'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-utensils text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_meal'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$5</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_meal_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('meal_item_1'); ?></li>
                        <li>• <?php echo __('meal_item_2'); ?></li>
                        <li>• <?php echo __('meal_item_3'); ?></li>
                        <li>• <?php echo __('meal_item_4'); ?></li>
                    </ul>
                </div>

                <!-- مساعدة عائلة -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="family_aid" data-amount="50" data-name="<?php echo __('package_family_aid'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-home text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_family_aid'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$50</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_family_aid_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('family_aid_item_1'); ?></li>
                        <li>• <?php echo __('family_aid_item_2'); ?></li>
                        <li>• <?php echo __('family_aid_item_3'); ?></li>
                        <li>• <?php echo __('family_aid_item_4'); ?></li>
                    </ul>
                </div>

                <!-- مأوى آمن -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="shelter" data-amount="100" data-name="<?php echo __('package_shelter'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_shelter'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$100</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_shelter_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('shelter_item_1'); ?></li>
                        <li>• <?php echo __('shelter_item_2'); ?></li>
                        <li>• <?php echo __('shelter_item_3'); ?></li>
                        <li>• <?php echo __('shelter_item_4'); ?></li>
                    </ul>
                </div>

                <!-- مساعدة طبية -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="medical" data-amount="75" data-name="<?php echo __('package_medical'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-md text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_medical'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$75</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_medical_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('medical_item_1'); ?></li>
                        <li>• <?php echo __('medical_item_2'); ?></li>
                        <li>• <?php echo __('medical_item_3'); ?></li>
                        <li>• <?php echo __('medical_item_4'); ?></li>
                    </ul>
                </div>

                <!-- بطانية شتوية -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="blanket" data-amount="20" data-name="<?php echo __('package_blanket'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-bed text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_blanket'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$20</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_blanket_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('blanket_item_1'); ?></li>
                        <li>• <?php echo __('blanket_item_2'); ?></li>
                        <li>• <?php echo __('blanket_item_3'); ?></li>
                        <li>• <?php echo __('blanket_item_4'); ?></li>
                    </ul>
                </div>

                <!-- ملابس شتوية -->
                <div class="donation-package bg-gray-800 p-6 rounded-lg border border-gray-700" data-package="clothes" data-amount="30" data-name="<?php echo __('package_clothes'); ?>">
                    <div class="text-center mb-4">
                        <i class="fas fa-tshirt text-4xl text-red-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 text-center"><?php echo __('package_clothes'); ?></h3>
                    <div class="text-3xl font-bold text-red-500 text-center mb-4">$30</div>
                    <p class="text-gray-300 text-center mb-4"><?php echo __('package_clothes_desc'); ?></p>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>• <?php echo __('clothes_item_1'); ?></li>
                        <li>• <?php echo __('clothes_item_2'); ?></li>
                        <li>• <?php echo __('clothes_item_3'); ?></li>
                        <li>• <?php echo __('clothes_item_4'); ?></li>
                    </ul>
                </div>
            </div>

            <!-- Custom Amount -->
            <div class="text-center mb-12">
                <h3 class="text-xl font-bold text-white mb-4"><?php echo __('custom_amount'); ?></h3>
                <div class="flex flex-wrap justify-center gap-4">
                    <button class="custom-amount bg-gray-700 hover:bg-red-600 text-white font-bold py-2 px-6 rounded" data-amount="10">$10</button>
                    <button class="custom-amount bg-gray-700 hover:bg-red-600 text-white font-bold py-2 px-6 rounded" data-amount="25">$25</button>
                    <button class="custom-amount bg-gray-700 hover:bg-red-600 text-white font-bold py-2 px-6 rounded" data-amount="50">$50</button>
                    <button class="custom-amount bg-gray-700 hover:bg-red-600 text-white font-bold py-2 px-6 rounded" data-amount="100">$100</button>
                    <button class="custom-amount bg-gray-700 hover:bg-red-600 text-white font-bold py-2 px-6 rounded" data-amount="250">$250</button>
                </div>
            </div>

            <!-- Important Notice -->
            <div class="max-w-2xl mx-auto mb-8 bg-gradient-to-r from-red-900 to-red-700 p-6 rounded-lg border border-red-600">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-2xl"></i>
                    </div>
                    <div class="<?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>">
                        <h4 class="text-lg font-bold text-white mb-2">
                            <i class="fas fa-info-circle <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                            <?php echo $is_rtl ? 'تنويه مهم' : 'Important Notice'; ?>
                        </h4>
                        <p class="text-gray-200 leading-relaxed">
                            <?php echo __('donation_important_notice'); ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Donation Form -->
            <div class="max-w-md mx-auto bg-gray-800 p-8 rounded-lg border border-gray-700">
                <h3 class="text-2xl font-bold text-white mb-6 text-center"><?php echo __('donation_form_title'); ?></h3>
                
                <form id="donationForm">
                    <input type="hidden" name="action" value="submit_donation">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-4">
                        <label class="block text-white font-medium mb-2"><?php echo __('donation_package'); ?></label>
                        <input type="text" id="donationPackage" name="package" readonly class="w-full p-3 border border-gray-700 rounded-lg bg-gray-900 text-red-400 font-bold">
                    </div>

                    <div class="mb-4">
                        <label class="block text-white font-medium mb-2"><?php echo __('donation_amount'); ?></label>
                        <input type="number" id="donationAmount" name="amount" step="0.01" min="<?php echo MIN_DONATION_AMOUNT; ?>" max="<?php echo MAX_DONATION_AMOUNT; ?>" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-white font-medium mb-2"><?php echo __('donor_name'); ?></label>
                        <input type="text" name="name" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-white font-medium mb-2"><?php echo __('donor_email'); ?></label>
                        <input type="email" name="email" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-white font-medium mb-2"><?php echo __('transaction_id'); ?></label>
                        <input type="text" name="txid" class="w-full p-3 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 bg-gray-900 text-white" placeholder="<?php echo __('message_placeholder'); ?>">
                        <p class="text-gray-400 text-sm mt-1"><?php echo __('txid_note'); ?></p>
                    </div>
                    
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-lg">
                        <span class="btn-text">
                            <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('submit_donation'); ?>
                        </span>
                        <span class="btn-loading hidden">
                            <i class="fas fa-spinner fa-spin <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('processing'); ?>
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Wallet Info -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-red-500 mb-6"><?php echo __('wallet_info'); ?></h2>
                <div class="bg-gray-900 p-6 rounded-lg border border-gray-700">
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('wallet_address'); ?></h3>
                    <div class="bg-gray-800 p-4 rounded border border-gray-600 mb-4">
                        <code class="text-green-400 text-sm break-all"><?php echo TRON_WALLET_ADDRESS; ?></code>
                    </div>
                    <p class="text-gray-300 text-sm">
                        <i class="fas fa-info-circle text-blue-400 <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                        <?php echo __('wallet_warning'); ?>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Notification Container -->
    <div id="notificationContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <script>
        // Page initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Get selected package and amount from URL
            const urlParams = new URLSearchParams(window.location.search);
            const selectedPackage = urlParams.get('package') || '<?php echo $selected_package; ?>';
            const selectedAmount = urlParams.get('amount') || <?php echo $selected_amount; ?>;

            // Find and select the package
            const packageElement = document.querySelector(`[data-package="${selectedPackage}"]`);
            if (packageElement) {
                selectPackage(packageElement);

                // Set the amount if provided
                if (selectedAmount > 0) {
                    document.getElementById('donationAmount').value = selectedAmount;
                }
            } else {
                // Select hot meal as default
                const defaultPackage = document.querySelector('[data-package="meal"]');
                if (defaultPackage) {
                    selectPackage(defaultPackage);
                }
            }

            // Add event listeners
            initializeDonationPage();
        });

        function initializeDonationPage() {
            // Donation package event listeners
            document.querySelectorAll('.donation-package').forEach(package => {
                package.addEventListener('click', function() {
                    selectPackage(this);
                });
            });

            // Custom amount event listeners
            document.querySelectorAll('.custom-amount').forEach(button => {
                button.addEventListener('click', function() {
                    const amount = this.getAttribute('data-amount');
                    document.getElementById('donationAmount').value = amount;
                    document.getElementById('donationPackage').value = '<?php echo __('custom_amount'); ?>';

                    // Remove selection from packages
                    document.querySelectorAll('.donation-package').forEach(p => {
                        p.classList.remove('selected');
                    });
                });
            });

            // Form handling
            document.getElementById('donationForm').addEventListener('submit', handleDonationSubmit);
        }
        
        function selectPackage(packageElement) {
            // Remove selection from all packages
            document.querySelectorAll('.donation-package').forEach(p => {
                p.classList.remove('selected');
            });

            // Select current package
            packageElement.classList.add('selected');

            // Update form
            const packageName = packageElement.getAttribute('data-name');
            const packageAmount = packageElement.getAttribute('data-amount');
            
            document.getElementById('donationPackage').value = packageName;
            document.getElementById('donationAmount').value = packageAmount;
        }
        
        // استخدام دالة معالجة التبرع من main.js
        function handleDonationSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            const formData = new FormData(form);
            
            // التحقق من صحة البيانات
            if (!validateDonationForm(formData)) {
                return;
            }
            
            // تغيير حالة الزر
            setButtonLoading(submitBtn, true);
            
            // إرسال البيانات
            fetch('donate.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                setButtonLoading(submitBtn, false);
                
                if (data.success) {
                    showNotification(data.message, 'success');
                    form.reset();
                    // إعادة تحديد الحزمة الافتراضية
                    const defaultPackage = document.querySelector('[data-package="meal"]');
                    if (defaultPackage) {
                        selectPackage(defaultPackage);
                    }
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                setButtonLoading(submitBtn, false);
                showNotification('حدث خطأ أثناء إرسال التبرع. يرجى المحاولة مرة أخرى.', 'error');
            });
        }
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
