<?php
/**
 * ملف الإعداد السريع لموقع يداً بيد لأجل غزة
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'charity_gaza';

// استبدال <html lang='ar' dir='rtl'> بـ:
$current_lang = 'ar'; // اجعلها ديناميكية حسب اللغة المختارة
$text_direction = 'rtl'; // اجعلها ديناميكية حسب اللغة المختارة

echo "<!DOCTYPE html>
<html lang='<?php echo $current_lang; ?>' dir='<?php echo $text_direction; ?>'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد موقع يداً بيد لأجل غزة</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Tajawal', sans-serif; background: linear-gradient(135deg, #1f2937 0%, #111827 100%); }
    </style>
</head>
<body class='min-h-screen flex items-center justify-center p-4'>
    <div class='bg-gray-800 p-8 rounded-xl shadow-2xl w-full max-w-2xl'>
        <div class='text-center mb-8'>
            <h1 class='text-3xl font-bold text-red-500 mb-2'>إعداد موقع يداً بيد لأجل غزة</h1>
            <p class='text-gray-300'>جاري إعداد قاعدة البيانات...</p>
        </div>
        <div class='space-y-4'>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='bg-green-600 text-white p-3 rounded'>✅ تم الاتصال بـ MySQL بنجاح</div>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='bg-green-600 text-white p-3 rounded'>✅ تم إنشاء قاعدة البيانات: $db_name</div>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة ملف SQL وتنفيذه
    $sql_file = __DIR__ . '/database/charity_gaza.sql';
    if (file_exists($sql_file)) {
        $sql = file_get_contents($sql_file);
        
        // تقسيم الاستعلامات
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|#)/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        echo "<div class='bg-green-600 text-white p-3 rounded'>✅ تم إنشاء جميع الجداول بنجاح</div>";
    } else {
        echo "<div class='bg-yellow-600 text-white p-3 rounded'>⚠️ ملف SQL غير موجود، سيتم إنشاء الجداول يدوياً</div>";
        
        // إنشاء الجداول يدوياً
        $tables = [
            "CREATE TABLE IF NOT EXISTS donors (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                phone VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS donations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                donor_id INT,
                donor_name VARCHAR(255) NOT NULL,
                donor_email VARCHAR(255) NOT NULL,
                package_type VARCHAR(100) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                txid VARCHAR(255),
                status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending',
                verification_attempts INT DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (donor_id) REFERENCES donors(id) ON DELETE SET NULL,
                INDEX idx_status (status),
                INDEX idx_txid (txid)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS counters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                meals_count INT DEFAULT 0,
                families_count INT DEFAULT 0,
                children_count INT DEFAULT 0,
                donors_count INT DEFAULT 0,
                total_donations DECIMAL(12,2) DEFAULT 0.00,
                meals_goal DECIMAL(12,2) DEFAULT 100000.00,
                families_goal DECIMAL(12,2) DEFAULT 50000.00,
                children_goal DECIMAL(12,2) DEFAULT 25000.00,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS testimonials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                role VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                image_url VARCHAR(500),
                is_active BOOLEAN DEFAULT TRUE,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS contacts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                subject VARCHAR(255),
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_read (is_read)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                role ENUM('admin', 'moderator') DEFAULT 'moderator',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($tables as $table_sql) {
            $pdo->exec($table_sql);
        }
        
        echo "<div class='bg-green-600 text-white p-3 rounded'>✅ تم إنشاء الجداول الأساسية</div>";
    }
    
    // إدراج البيانات الافتراضية
    $pdo->exec("INSERT IGNORE INTO counters (meals_count, families_count, children_count, donors_count) VALUES (15420, 8750, 12300, 2840)");
    
    $pdo->exec("INSERT IGNORE INTO admin_users (username, password_hash, full_name, email, role) VALUES ('admin', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin')");
    
    $pdo->exec("INSERT IGNORE INTO testimonials (name, role, message, display_order) VALUES 
        ('أحمد محمد', 'أب لثلاثة أطفال', 'بفضل تبرعاتكم استطعنا إطعام أطفالنا لأسبوع كامل. جزاكم الله خيراً', 1),
        ('فاطمة أحمد', 'أم لخمسة أطفال', 'الوجبات الساخنة التي وصلتنا أنقذت حياة أطفالي من الجوع. شكراً لكل متبرع', 2),
        ('محمد عبدالله', 'مسن من غزة', 'في هذا البرد القارس، البطانيات التي وصلتنا كانت نعمة من الله', 3)");
    
    echo "<div class='bg-green-600 text-white p-3 rounded'>✅ تم إدراج البيانات الافتراضية</div>";
    
    echo "<div class='bg-blue-600 text-white p-4 rounded mt-6'>
            <h3 class='font-bold text-lg mb-2'>🎉 تم الإعداد بنجاح!</h3>
            <p class='mb-4'>يمكنك الآن استخدام الموقع:</p>
            <div class='space-y-2'>
                <p><strong>الموقع الرئيسي:</strong> <a href='index.php' class='text-blue-200 hover:underline'>http://localhost/gaza/charity_gaza/</a></p>
                <p><strong>لوحة الإدارة:</strong> <a href='admin/' class='text-blue-200 hover:underline'>http://localhost/gaza/charity_gaza/admin/</a></p>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='bg-red-600 text-white p-4 rounded'>
            <h3 class='font-bold text-lg mb-2'>❌ خطأ في الإعداد</h3>
            <p class='mb-2'><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <div class='mt-4 p-3 bg-red-700 rounded'>
                <h4 class='font-bold mb-2'>خطوات الحل:</h4>
                <ol class='list-decimal list-inside space-y-1 text-sm'>
                    <li>تأكد من تشغيل XAMPP (Apache + MySQL)</li>
                    <li>تأكد من إعدادات قاعدة البيانات في أعلى هذا الملف</li>
                    <li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>
                    <li>جرب تغيير كلمة مرور MySQL إذا كانت مختلفة</li>
                </ol>
            </div>
          </div>";
}

echo "    </div>
    </div>
</body>
</html>";
?>
