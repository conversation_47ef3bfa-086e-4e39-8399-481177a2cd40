<?php
/**
 * صفحة الشهادات والتوصيات
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// جلب الشهادات من قاعدة البيانات
$testimonials = selectQuery("
    SELECT * FROM testimonials 
    WHERE is_active = 1 
    ORDER BY display_order ASC, created_at DESC
") ?: [];

// معلومات الصفحة
$page_title = __('testimonials_title');
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/favicon.png">
    <link rel="shortcut icon" type="image/png" href="<?php echo ASSETS_URL; ?>/favicon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #111;
            color: #f0f0f0;
        }
        .testimonial-card {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            border: 1px solid #374151;
            transition: all 0.3s ease;
        }
        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(230, 57, 70, 0.2);
            border-color: #e63946;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- اللوجو -->
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-heart text-white text-xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>">
                        <a href="index.php"><?php echo __('site_name'); ?></a>
                    </h1>
                </div>

                <!-- التنقل الرئيسي -->
                <nav class="hidden lg:block">
                    <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                        <li><a href="index.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_home'); ?></a></li>
                        <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_kitchen'); ?></a></li>
                        <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_tragedy'); ?></a></li>
                        <li><a href="donate.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_donate'); ?></a></li>
                        <li><a href="testimonials.php" class="text-red-500 font-medium"><?php echo __('nav_testimonials'); ?></a></li>
                        <li><a href="contact.php" class="text-gray-300 hover:text-red-500 transition-colors"><?php echo __('nav_contact'); ?></a></li>
                    </ul>
                </nav>

                <!-- منتقي اللغة -->
                <div class="flex items-center">
                    <?php echo renderLanguageSelector(); ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-red-900 to-red-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-quote-right text-6xl text-yellow-400"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('testimonials_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('testimonials_description'); ?></p>
        </div>
    </section>

    <!-- Statistics -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div class="p-6">
                    <i class="fas fa-users text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">15,420</div>
                    <div class="text-gray-300"><?php echo __('stats_meals'); ?></div>
                </div>
                <div class="p-6">
                    <i class="fas fa-home text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">8,750</div>
                    <div class="text-gray-300"><?php echo __('stats_families'); ?></div>
                </div>
                <div class="p-6">
                    <i class="fas fa-heart text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">2,840</div>
                    <div class="text-gray-300"><?php echo __('generous_donors'); ?></div>
                </div>
                <div class="p-6">
                    <i class="fas fa-smile text-4xl text-red-500 mb-4"></i>
                    <div class="text-3xl font-bold text-white mb-2">98%</div>
                    <div class="text-gray-300"><?php echo __('satisfaction_rate'); ?></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('testimonials_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('testimonials_description'); ?></p>
            </div>

            <?php if (!empty($testimonials)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($testimonials as $testimonial): ?>
                        <div class="testimonial-card p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-16 h-16 rounded-full bg-red-600 flex items-center justify-center ml-4">
                                    <?php if (!empty($testimonial['image_url'])): ?>
                                        <img src="<?php echo htmlspecialchars($testimonial['image_url']); ?>" alt="<?php echo htmlspecialchars($testimonial['name']); ?>" class="w-16 h-16 rounded-full object-cover">
                                    <?php else: ?>
                                        <i class="fas fa-user text-white text-xl"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h3 class="text-white font-bold text-lg"><?php echo htmlspecialchars($testimonial['name']); ?></h3>
                                    <p class="text-gray-400 text-sm"><?php echo htmlspecialchars($testimonial['role']); ?></p>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <i class="fas fa-quote-right text-red-500 text-2xl mb-2"></i>
                                <p class="text-gray-300 italic leading-relaxed"><?php echo htmlspecialchars($testimonial['message']); ?></p>
                            </div>
                            
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <span class="text-gray-400 text-sm mr-2">5/5</span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <!-- Default testimonials -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-red-600 flex items-center justify-center <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>">
                                <span class="text-white text-xl font-bold"><?php echo __('testimonial_1_initial'); ?></span>
                            </div>
                            <div>
                                <h3 class="text-white font-bold text-lg"><?php echo __('testimonial_1_name'); ?></h3>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_1_role'); ?></p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <i class="fas fa-quote-right text-red-500 text-2xl mb-2"></i>
                            <p class="text-gray-300 italic leading-relaxed"><?php echo __('testimonial_1_message'); ?></p>
                        </div>
                        <div class="flex items-center text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span class="text-gray-400 text-sm <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>">5/5</span>
                        </div>
                    </div>

                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-red-600 flex items-center justify-center <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>">
                                <span class="text-white text-xl font-bold"><?php echo __('testimonial_2_initial'); ?></span>
                            </div>
                            <div>
                                <h3 class="text-white font-bold text-lg"><?php echo __('testimonial_2_name'); ?></h3>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_2_role'); ?></p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <i class="fas fa-quote-right text-red-500 text-2xl mb-2"></i>
                            <p class="text-gray-300 italic leading-relaxed"><?php echo __('testimonial_2_message'); ?></p>
                        </div>
                        <div class="flex items-center text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span class="text-gray-400 text-sm <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>">5/5</span>
                        </div>
                    </div>

                    <div class="testimonial-card p-6 rounded-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-red-600 flex items-center justify-center <?php echo $is_rtl ? 'mr-4' : 'ml-4'; ?>">
                                <span class="text-white text-xl font-bold"><?php echo __('testimonial_3_initial'); ?></span>
                            </div>
                            <div>
                                <h3 class="text-white font-bold text-lg"><?php echo __('testimonial_3_name'); ?></h3>
                                <p class="text-gray-400 text-sm"><?php echo __('testimonial_3_role'); ?></p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <i class="fas fa-quote-right text-red-500 text-2xl mb-2"></i>
                            <p class="text-gray-300 italic leading-relaxed"><?php echo __('testimonial_3_message'); ?></p>
                        </div>
                        <div class="flex items-center text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span class="text-gray-400 text-sm <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>">5/5</span>
                        </div>
                    </div>


                </div>
            <?php endif; ?>
        </div>
    </section>



    <!-- Call to Action -->
    <section class="py-20 bg-red-900">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-white mb-4"><?php echo __('kitchen_cta_title'); ?></h2>
            <p class="text-xl text-gray-200 mb-8"><?php echo __('kitchen_cta_desc'); ?></p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="donate.php" class="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-lg text-lg">
                    <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('donate_now'); ?>
                </a>
                <a href="contact.php" class="border-2 border-white text-white hover:bg-white hover:text-red-900 font-bold py-4 px-8 rounded-lg text-lg">
                    <i class="fas fa-envelope <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i><?php echo __('share_story'); ?>
                </a>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
