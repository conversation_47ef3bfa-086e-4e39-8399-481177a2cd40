<?php
/**
 * <PERSON><PERSON>t to add donation notice to all language files
 */

$languages = [
    'es' => 'Aviso importante: Por favor, realice primero su generosa donación a través de nuestra dirección TRC20, luego complete este formulario para que podamos conocer sus amables nombres y reconocer sus benditas contribuciones.',
    'ru' => 'Важное уведомление: Пожалуйста, сначала сделайте ваше щедрое пожертвование через наш адрес TRC20, затем заполните эту форму, чтобы мы могли узнать ваши добрые имена и признать ваши благословенные вклады.',
    'sv' => 'Viktigt meddelande: Vänligen gör först din generösa donation via vår TRC20-adress, fyll sedan i detta formulär så att vi kan känna till dina vänliga namn och erkänna dina välsignade bidrag.',
    'zh' => '重要提示：请先通过我们的TRC20地址进行您的慷慨捐赠，然后填写此表格，以便我们了解您的善良姓名并确认您的神圣贡献。',
    'no' => 'Viktig merknad: Vennligst gjør først din sjenerøse donasjon via vår TRC20-adresse, fyll deretter ut dette skjemaet slik at vi kan kjenne dine vennlige navn og anerkjenne dine velsignede bidrag.',
    'pt' => 'Aviso importante: Por favor, faça primeiro sua doação generosa através do nosso endereço TRC20, depois preencha este formulário para que possamos conhecer seus nomes gentis e reconhecer suas contribuições abençoadas.',
    'tr' => 'Önemli uyarı: Lütfen önce TRC20 adresimiz üzerinden cömert bağışınızı yapın, sonra nazik isimlerinizi öğrenebilmemiz ve mübarek katkılarınızı kabul edebilmemiz için bu formu doldurun.',
    'id' => 'Pemberitahuan penting: Silakan lakukan donasi murah hati Anda terlebih dahulu melalui alamat TRC20 kami, kemudian isi formulir ini agar kami dapat mengetahui nama-nama baik Anda dan mengakui kontribusi yang diberkati.',
    'ja' => '重要なお知らせ：まずTRC20アドレスを通じて寛大な寄付を行い、その後このフォームにご記入ください。お名前を知り、祝福された貢献を認識できるようになります。',
    'ms' => 'Notis penting: Sila buat derma murah hati anda dahulu melalui alamat TRC20 kami, kemudian isi borang ini supaya kami dapat mengetahui nama-nama baik anda dan mengiktiraf sumbangan yang diberkati.'
];

foreach ($languages as $lang => $notice) {
    $file = "languages/{$lang}.php";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Find the donation_form_notice line and add the new notice after it
        $pattern = "/('donation_form_notice' => '[^']*',)/";
        $replacement = "$1\n    'donation_important_notice' => '{$notice}',";
        
        $newContent = preg_replace($pattern, $replacement, $content);
        
        if ($newContent !== $content) {
            file_put_contents($file, $newContent);
            echo "Updated {$lang}.php\n";
        } else {
            echo "No changes needed for {$lang}.php\n";
        }
    } else {
        echo "File {$file} not found\n";
    }
}

echo "All language files updated!\n";
?>
