<?php
/**
 * صفحة إدارة الرسائل
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                $contact_id = intval($_POST['contact_id']);
                updateQuery("UPDATE contacts SET is_read = 1 WHERE id = ?", [$contact_id]);
                logAdminActivity('تحديد رسالة كمقروءة', 'contacts', $contact_id);
                $message = 'تم تحديد الرسالة كمقروءة';
                $message_type = 'success';
                break;
                
            case 'mark_unread':
                $contact_id = intval($_POST['contact_id']);
                updateQuery("UPDATE contacts SET is_read = 0 WHERE id = ?", [$contact_id]);
                logAdminActivity('تحديد رسالة كغير مقروءة', 'contacts', $contact_id);
                $message = 'تم تحديد الرسالة كغير مقروءة';
                $message_type = 'success';
                break;
                
            case 'add_notes':
                $contact_id = intval($_POST['contact_id']);
                $notes = sanitizeInput($_POST['notes']);
                updateQuery("UPDATE contacts SET admin_notes = ? WHERE id = ?", [$notes, $contact_id]);
                logAdminActivity('إضافة ملاحظات لرسالة', 'contacts', $contact_id);
                $message = 'تم حفظ الملاحظات';
                $message_type = 'success';
                break;
                
            case 'delete_contact':
                $contact_id = intval($_POST['contact_id']);
                $contact = selectOne("SELECT * FROM contacts WHERE id = ?", [$contact_id]);
                deleteQuery("DELETE FROM contacts WHERE id = ?", [$contact_id]);
                logAdminActivity('حذف رسالة', 'contacts', $contact_id, $contact);
                $message = 'تم حذف الرسالة';
                $message_type = 'success';
                break;
        }
    }
}

// فلترة البيانات
$status_filter = $_GET['status'] ?? 'all';
$search = sanitizeInput($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = [];
$params = [];

if ($status_filter === 'unread') {
    $where_conditions[] = "is_read = 0";
} elseif ($status_filter === 'read') {
    $where_conditions[] = "is_read = 1";
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// جلب الرسائل
$contacts = selectQuery(
    "SELECT c.*, DATE_FORMAT(c.created_at, '%Y-%m-%d %H:%i') as formatted_date 
     FROM contacts c 
     {$where_clause} 
     ORDER BY c.is_read ASC, c.created_at DESC 
     LIMIT {$per_page} OFFSET {$offset}",
    $params
);

// عدد الرسائل الإجمالي
$total_contacts = selectOne(
    "SELECT COUNT(*) as count FROM contacts c {$where_clause}",
    $params
)['count'] ?? 0;

$total_pages = ceil($total_contacts / $per_page);

// إحصائيات سريعة
$stats = [
    'total' => selectOne("SELECT COUNT(*) as count FROM contacts")['count'] ?? 0,
    'unread' => selectOne("SELECT COUNT(*) as count FROM contacts WHERE is_read = 0")['count'] ?? 0,
    'read' => selectOne("SELECT COUNT(*) as count FROM contacts WHERE is_read = 1")['count'] ?? 0
];

$page_title = 'إدارة الرسائل';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid #334155;
        }
        
        .table-container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
        }
        
        .nav-link {
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background-color: rgba(230, 57, 70, 0.1);
            border-right: 3px solid #e63946;
        }
        
        .nav-link.active {
            background-color: rgba(230, 57, 70, 0.2);
            border-right: 3px solid #e63946;
        }
        
        .unread-message {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            background-color: #1e293b;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-slate-900">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">لوحة الإدارة</h1>
                        <p class="text-sm text-gray-400"><?php echo SITE_NAME; ?></p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="index.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="mr-3">الرئيسية</span>
                    </a>
                    <a href="donations.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-hand-holding-heart w-5"></i>
                        <span class="mr-3">التبرعات</span>
                    </a>
                    <a href="contacts.php" class="nav-link active flex items-center px-4 py-3 text-white rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="mr-3">الرسائل</span>
                        <?php if ($stats['unread'] > 0): ?>
                            <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full mr-auto">
                                <?php echo $stats['unread']; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <a href="testimonials.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-quote-right w-5"></i>
                        <span class="mr-3">الشهادات</span>
                    </a>
                    <a href="achievements.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-trophy w-5"></i>
                        <span class="mr-3">الإنجازات</span>
                    </a>
                    <a href="counters.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span class="mr-3">العدادات</span>
                    </a>
                    <a href="settings.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-cog w-5"></i>
                        <span class="mr-3">الإعدادات</span>
                    </a>
                </nav>
            </div>
            
            <!-- User Info -->
            <div class="absolute bottom-0 w-full p-6 border-t border-gray-700">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></p>
                        <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['admin_role']); ?></p>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-400" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-slate-800 border-b border-slate-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-white">إدارة الرسائل</h1>
                        <p class="text-gray-400">عرض وإدارة رسائل التواصل الواردة</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="../index.php" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>عرض الموقع
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <main class="p-6">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-600' : 'bg-red-600'; ?> text-white">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-white"><?php echo number_format($stats['total']); ?></p>
                            <p class="text-gray-400 text-sm">إجمالي الرسائل</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-blue-400"><?php echo number_format($stats['unread']); ?></p>
                            <p class="text-gray-400 text-sm">غير مقروءة</p>
                        </div>
                    </div>
                    <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-400"><?php echo number_format($stats['read']); ?></p>
                            <p class="text-gray-400 text-sm">مقروءة</p>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="table-container rounded-xl p-6 mb-6">
                    <form method="GET" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input 
                                type="text" 
                                name="search" 
                                placeholder="البحث بالاسم، البريد، أو الرسالة..." 
                                value="<?php echo htmlspecialchars($search); ?>"
                                class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500"
                            >
                        </div>
                        <div>
                            <select name="status" class="px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الرسائل</option>
                                <option value="unread" <?php echo $status_filter === 'unread' ? 'selected' : ''; ?>>غير مقروءة</option>
                                <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>مقروءة</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-search mr-2"></i>بحث
                        </button>
                        <a href="contacts.php" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-times mr-2"></i>إعادة تعيين
                        </a>
                    </form>
                </div>
                
                <!-- Messages List -->
                <div class="space-y-4">
                    <?php if (!empty($contacts)): ?>
                        <?php foreach ($contacts as $contact): ?>
                            <div class="table-container rounded-xl p-6 <?php echo !$contact['is_read'] ? 'unread-message' : ''; ?>">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <h3 class="text-lg font-bold text-white"><?php echo htmlspecialchars($contact['name']); ?></h3>
                                            <?php if (!$contact['is_read']): ?>
                                                <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full mr-2">جديد</span>
                                            <?php endif; ?>
                                        </div>
                                        <p class="text-gray-400 text-sm mb-2">
                                            <i class="fas fa-envelope mr-1"></i><?php echo htmlspecialchars($contact['email']); ?>
                                            <span class="mr-4">
                                                <i class="fas fa-tag mr-1"></i><?php echo htmlspecialchars($contact['subject'] ?? 'بدون موضوع'); ?>
                                            </span>
                                            <span class="mr-4">
                                                <i class="fas fa-clock mr-1"></i><?php echo $contact['formatted_date']; ?>
                                            </span>
                                        </p>
                                        <p class="text-gray-300 mb-4"><?php echo nl2br(htmlspecialchars($contact['message'])); ?></p>
                                        
                                        <?php if (!empty($contact['admin_notes'])): ?>
                                            <div class="bg-slate-800 p-3 rounded-lg border border-slate-600">
                                                <h4 class="text-sm font-bold text-yellow-400 mb-1">ملاحظات المشرف:</h4>
                                                <p class="text-gray-300 text-sm"><?php echo nl2br(htmlspecialchars($contact['admin_notes'])); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="flex flex-col space-y-2 mr-4">
                                        <?php if (!$contact['is_read']): ?>
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="contact_id" value="<?php echo $contact['id']; ?>">
                                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs" title="تحديد كمقروء">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="mark_unread">
                                                <input type="hidden" name="contact_id" value="<?php echo $contact['id']; ?>">
                                                <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs" title="تحديد كغير مقروء">
                                                    <i class="fas fa-eye-slash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <button onclick="addNotes(<?php echo htmlspecialchars(json_encode($contact)); ?>)" class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs" title="إضافة ملاحظات">
                                            <i class="fas fa-sticky-note"></i>
                                        </button>
                                        
                                        <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                            <input type="hidden" name="action" value="delete_contact">
                                            <input type="hidden" name="contact_id" value="<?php echo $contact['id']; ?>">
                                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="table-container rounded-xl p-12 text-center">
                            <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-400">لا توجد رسائل مطابقة للبحث</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="mt-6 flex items-center justify-between">
                        <div class="text-gray-400 text-sm">
                            عرض <?php echo ($offset + 1); ?> إلى <?php echo min($offset + $per_page, $total_contacts); ?> من أصل <?php echo $total_contacts; ?> رسالة
                        </div>
                        <div class="flex space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" class="bg-slate-700 hover:bg-slate-600 text-white px-3 py-2 rounded">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" 
                                   class="<?php echo $i === $page ? 'bg-red-600' : 'bg-slate-700 hover:bg-slate-600'; ?> text-white px-3 py-2 rounded">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" class="bg-slate-700 hover:bg-slate-600 text-white px-3 py-2 rounded">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- Notes Modal -->
    <div id="notesModal" class="modal">
        <div class="modal-content">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-white">إضافة ملاحظات</h2>
                <button onclick="closeNotesModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" id="notesForm">
                <input type="hidden" name="action" value="add_notes">
                <input type="hidden" name="contact_id" id="notesContactId">
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">معلومات الرسالة</label>
                    <div class="bg-slate-800 p-3 rounded-lg">
                        <p class="text-white font-bold" id="notesContactName"></p>
                        <p class="text-gray-400 text-sm" id="notesContactEmail"></p>
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="block text-gray-300 mb-2">ملاحظات المشرف</label>
                    <textarea name="notes" id="notesText" rows="5" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white resize-none" placeholder="أضف ملاحظاتك هنا..."></textarea>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        حفظ الملاحظات
                    </button>
                    <button type="button" onclick="closeNotesModal()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function addNotes(contact) {
            document.getElementById('notesContactId').value = contact.id;
            document.getElementById('notesContactName').textContent = contact.name;
            document.getElementById('notesContactEmail').textContent = contact.email;
            document.getElementById('notesText').value = contact.admin_notes || '';
            document.getElementById('notesModal').style.display = 'block';
        }
        
        function closeNotesModal() {
            document.getElementById('notesModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('notesModal');
            if (event.target === modal) {
                closeNotesModal();
            }
        }
    </script>
</body>
</html>
