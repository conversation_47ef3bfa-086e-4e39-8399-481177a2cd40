/**
 * ملف CSS الرئيسي لموقع يداً بيد لأجل غزة
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: #111;
    color: #f0f0f0;
    line-height: 1.6;
    direction: rtl;
}

/* خلفيات الأقسام */
.hero-bg {
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), 
                url('https://images.unsplash.com/photo-1632495288245-81b9d20c0cdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.kitchen-bg {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
                url('https://images.unsplash.com/photo-1632495288245-81b9d20c0cdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* صناديق العدادات */
.counter-box {
    transition: all 0.3s ease;
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid #e63946;
    backdrop-filter: blur(10px);
}

.counter-box:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(230, 57, 70, 0.5);
    background-color: rgba(0, 0, 0, 0.8);
}

/* أزرار التبرع */
.donate-btn {
    transition: all 0.3s ease;
    background-color: #e63946;
    position: relative;
    overflow: hidden;
}

.donate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(230, 57, 70, 0.4);
    background-color: #c1121f;
}

.donate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.donate-btn:hover::before {
    left: 100%;
}

/* الرسوم المتحركة */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.crying-icon {
    animation: cry 3s ease-in-out infinite;
    color: #e63946;
}

@keyframes cry {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* الصور المأساوية */
.tragic-image {
    filter: grayscale(30%) brightness(0.8);
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
}

.tragic-image:hover {
    filter: grayscale(0%) brightness(1);
    transform: scale(1.02);
}

/* الأقسام المأساوية */
.tragic-section {
    margin-bottom: 4rem;
    opacity: 0;
    transform: translateY(50px);
    animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #1f2937;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    padding: 10px 15px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e63946;
}

/* أشرطة التقدم */
.progress-bar {
    background-color: #374151;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
    position: relative;
}

.progress-fill {
    background: linear-gradient(90deg, #e63946, #dc2626);
    height: 100%;
    transition: width 2s ease-in-out;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    min-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background-color: #10b981;
    border-left: 4px solid #059669;
}

.notification.error {
    background-color: #ef4444;
    border-left: 4px solid #dc2626;
}

.notification.info {
    background-color: #3b82f6;
    border-left: 4px solid #2563eb;
}

/* مؤشر التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* بطاقات التبرع */
.donation-card {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.donation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e63946, #dc2626);
}

.donation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(230, 57, 70, 0.2);
    border-color: #e63946;
}

/* بطاقات الشهادات */
.testimonial-card {
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid #374151;
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border-color: #e63946;
}

/* نموذج التواصل */
.contact-form {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
}

.contact-form input,
.contact-form textarea {
    background-color: #111827;
    border: 1px solid #374151;
    color: #f9fafb;
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    border-color: #e63946;
    box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
    outline: none;
}

/* تحسين قسم About */
#about {
    margin-top: 4rem;
    margin-bottom: 4rem;
    padding-top: 5rem;
    padding-bottom: 5rem;
}

/* تحسين بطاقات المهمة والرؤية والقيم */
.mission-vision-cards {
    margin-bottom: 5rem;
}

.mission-vision-cards .text-center {
    padding: 2.5rem 1.5rem;
    background: linear-gradient(145deg, rgba(31, 41, 55, 0.6) 0%, rgba(17, 24, 39, 0.6) 100%);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(230, 57, 70, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.mission-vision-cards .text-center::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e63946, #dc2626, #b91c1c);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.mission-vision-cards .text-center:hover::before {
    transform: scaleX(1);
}

.mission-vision-cards .text-center:hover {
    transform: translateY(-8px);
    border-color: rgba(230, 57, 70, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    background: linear-gradient(145deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%);
}

.mission-vision-cards .w-16 {
    width: 5rem !important;
    height: 5rem !important;
    transition: all 0.4s ease;
    position: relative;
    margin-bottom: 1.5rem !important;
}

.mission-vision-cards .w-16:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 25px rgba(230, 57, 70, 0.5);
}

.mission-vision-cards .fas {
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.mission-vision-cards .text-center:hover .fas {
    transform: scale(1.2);
}

/* تحسين القسم المأساوي */
.tragic-section {
    margin-top: 5rem;
    padding: 3rem 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(17, 24, 39, 0.3) 100%);
    border-radius: 1.5rem;
    border: 1px solid rgba(230, 57, 70, 0.1);
}

/* تحسين الصورة المأساوية */
.tragic-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    margin-bottom: 2rem;
}

.tragic-image {
    width: 100% !important;
    height: 400px !important;
    object-fit: cover !important;
    filter: grayscale(20%) brightness(0.85) contrast(1.1);
    transition: all 0.5s ease;
    border-radius: 1rem;
}

.tragic-image:hover {
    filter: grayscale(0%) brightness(1) contrast(1.2);
    transform: scale(1.02);
}

.tragic-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(230, 57, 70, 0.1) 0%, transparent 50%);
    border-radius: 1rem;
    pointer-events: none;
}

/* تحسين محتوى القسم المأساوي */
.tragic-content {
    padding: 0 2rem;
}

.tragic-content h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    position: relative;
}

.tragic-content h3::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #e63946, #dc2626);
    border-radius: 2px;
}

.tragic-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

/* تحسين صندوق الحقائق المصدمة */
.shocking-facts-box {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%) !important;
    border: 2px solid #e63946 !important;
    border-radius: 1rem !important;
    padding: 2rem !important;
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
}

.shocking-facts-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e63946, #dc2626, #b91c1c, #e63946);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.shocking-facts-box h3 {
    font-size: 1.5rem !important;
    margin-bottom: 1.5rem !important;
}

.shocking-facts-box ul li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(230, 57, 70, 0.2);
    transition: all 0.3s ease;
}

.shocking-facts-box ul li:last-child {
    border-bottom: none;
}

.shocking-facts-box ul li:hover {
    background: rgba(230, 57, 70, 0.1);
    padding-right: 1rem;
    border-radius: 0.5rem;
}

.shocking-facts-box ul li i {
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-bg {
        background-attachment: scroll;
    }
    
    .kitchen-bg {
        background-attachment: scroll;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        transform: translateY(-100px);
    }
    
    .notification.show {
        transform: translateY(0);
    }
    
    /* تحسينات للموبايل */
    #about {
        margin-top: 2rem;
        margin-bottom: 2rem;
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .mission-vision-cards {
        margin-bottom: 3rem;
    }
    
    .mission-vision-cards .text-center {
        padding: 2rem 1rem;
        margin-bottom: 1.5rem;
    }
    
    .tragic-section {
        margin-top: 3rem;
        padding: 2rem 1rem;
    }
    
    .tragic-image {
        height: 250px !important;
    }
    
    .tragic-content {
        padding: 0 1rem;
    }
    
    .tragic-content h3 {
        font-size: 1.5rem;
    }
    
    .shocking-facts-box {
        padding: 1.5rem !important;
    }
}

/* تحسينات للطباعة */
@media print {
    .modal,
    .notification,
    .donate-btn {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* تحسينات لإمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز للوحة المفاتيح */
button:focus,
input:focus,
textarea:focus,
a:focus {
    outline: 2px solid #e63946;
    outline-offset: 2px;
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
