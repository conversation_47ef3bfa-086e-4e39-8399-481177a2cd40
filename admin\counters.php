<?php
/**
 * صفحة إدارة العدادات والإحصائيات
 */

define('CHARITY_GAZA', true);

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$message_type = '';

// معالجة تحديث العدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_counters') {
        $meals_count = intval($_POST['meals_count']);
        $families_count = intval($_POST['families_count']);
        $children_count = intval($_POST['children_count']);
        $donors_count = intval($_POST['donors_count']);
        $meals_goal = floatval($_POST['meals_goal']);
        $families_goal = floatval($_POST['families_goal']);
        $children_goal = floatval($_POST['children_goal']);
        
        // التحقق من وجود سجل في جدول العدادات
        $existing = selectOne("SELECT id FROM counters LIMIT 1");
        
        if ($existing) {
            // تحديث السجل الموجود
            $updated = updateQuery(
                "UPDATE counters SET 
                 meals_count = ?, families_count = ?, children_count = ?, donors_count = ?,
                 meals_goal = ?, families_goal = ?, children_goal = ?
                 WHERE id = ?",
                [$meals_count, $families_count, $children_count, $donors_count,
                 $meals_goal, $families_goal, $children_goal, $existing['id']]
            );
        } else {
            // إنشاء سجل جديد
            $updated = insertQuery(
                "INSERT INTO counters (meals_count, families_count, children_count, donors_count, meals_goal, families_goal, children_goal) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)",
                [$meals_count, $families_count, $children_count, $donors_count,
                 $meals_goal, $families_goal, $children_goal]
            );
        }
        
        if ($updated) {
            logAdminActivity('تحديث العدادات والأهداف');
            $message = 'تم تحديث العدادات والأهداف بنجاح';
            $message_type = 'success';
        } else {
            $message = 'حدث خطأ أثناء تحديث العدادات';
            $message_type = 'error';
        }
    }
}

// جلب العدادات الحالية
try {
    $counters = selectOne("SELECT * FROM counters ORDER BY id DESC LIMIT 1");
} catch (Exception $e) {
    $counters = null;
}

// إذا لم توجد عدادات، استخدم القيم الافتراضية
if (!$counters) {
    $counters = [
        'meals_count' => 15420,
        'families_count' => 8750,
        'children_count' => 12300,
        'donors_count' => 2840,
        'meals_goal' => 100000.00,
        'families_goal' => 50000.00,
        'children_goal' => 25000.00
    ];
}

// حساب إحصائيات التبرعات الفعلية
try {
    $donation_stats = selectOne("
        SELECT
            COUNT(*) as total_donations,
            COUNT(DISTINCT donor_email) as unique_donors,
            SUM(CASE WHEN status = 'confirmed' THEN amount ELSE 0 END) as confirmed_amount,
            SUM(CASE WHEN status = 'confirmed' AND package_type LIKE '%meal%' THEN amount ELSE 0 END) as meals_amount,
            SUM(CASE WHEN status = 'confirmed' AND package_type LIKE '%family%' THEN amount ELSE 0 END) as families_amount,
            SUM(CASE WHEN status = 'confirmed' AND package_type LIKE '%shelter%' THEN amount ELSE 0 END) as shelter_amount
        FROM donations
    ");
} catch (Exception $e) {
    $donation_stats = null;
}

// التأكد من وجود البيانات
if (!$donation_stats) {
    $donation_stats = [
        'total_donations' => 0,
        'unique_donors' => 0,
        'confirmed_amount' => 0.00,
        'meals_amount' => 0.00,
        'families_amount' => 0.00,
        'shelter_amount' => 0.00
    ];
}

// حساب النسب المئوية للتقدم
$meals_progress = $counters['meals_goal'] > 0 ? ($donation_stats['meals_amount'] / $counters['meals_goal']) * 100 : 0;
$families_progress = $counters['families_goal'] > 0 ? ($donation_stats['families_amount'] / $counters['families_goal']) * 100 : 0;
$children_progress = $counters['children_goal'] > 0 ? ($donation_stats['shelter_amount'] / $counters['children_goal']) * 100 : 0;

$page_title = 'إدارة العدادات والإحصائيات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid #334155;
        }
        
        .table-container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
        }
        
        .nav-link {
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background-color: rgba(230, 57, 70, 0.1);
            border-right: 3px solid #e63946;
        }
        
        .nav-link.active {
            background-color: rgba(230, 57, 70, 0.2);
            border-right: 3px solid #e63946;
        }
        
        .progress-bar {
            background-color: #374151;
            border-radius: 10px;
            overflow: hidden;
            height: 20px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #e63946, #dc2626);
            height: 100%;
            transition: width 2s ease-in-out;
            border-radius: 10px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border-color: #e63946;
        }
    </style>
</head>
<body class="bg-slate-900">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">لوحة الإدارة</h1>
                        <p class="text-sm text-gray-400"><?php echo SITE_NAME; ?></p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="index.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="mr-3">الرئيسية</span>
                    </a>
                    <a href="donations.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-hand-holding-heart w-5"></i>
                        <span class="mr-3">التبرعات</span>
                    </a>
                    <a href="contacts.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="mr-3">الرسائل</span>
                    </a>
                    <a href="testimonials.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-quote-right w-5"></i>
                        <span class="mr-3">الشهادات</span>
                    </a>
                    <a href="achievements.php" class="nav-link flex items-center px-4 py-3 text-gray-300 hover:text-white rounded-lg">
                        <i class="fas fa-trophy w-5"></i>
                        <span class="mr-3">الإنجازات</span>
                    </a>
                    <a href="counters.php" class="nav-link active flex items-center px-4 py-3 text-white rounded-lg">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span class="mr-3">العدادات</span>
                    </a>
                </nav>
            </div>
            
            <!-- User Info -->
            <div class="absolute bottom-0 w-full p-6 border-t border-gray-700">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></p>
                        <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['admin_role']); ?></p>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-400" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-slate-800 border-b border-slate-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-white">إدارة العدادات والإحصائيات</h1>
                        <p class="text-gray-400">تحديث العدادات المعروضة في الموقع وتتبع التقدم</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="../index.php" target="_blank" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>عرض الموقع
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <main class="p-6">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-600' : 'bg-red-600'; ?> text-white">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Current Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-utensils text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">الوجبات المقدمة</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format((int)($counters['meals_count'] ?? 0)); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-home text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">العائلات المساعدة</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format((int)($counters['families_count'] ?? 0)); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-child text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">الأطفال المساعدين</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format((int)($counters['children_count'] ?? 0)); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card p-6 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-white text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <p class="text-gray-400 text-sm">المتبرعين</p>
                                <p class="text-2xl font-bold text-white"><?php echo number_format((int)($counters['donors_count'] ?? 0)); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Tracking -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Progress Bars -->
                    <div class="table-container rounded-xl p-6">
                        <h2 class="text-xl font-bold text-white mb-6">تقدم الأهداف المالية</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-white font-medium">الوجبات الساخنة</span>
                                    <span class="text-gray-400"><?php echo number_format((float)($meals_progress ?? 0), 1); ?>%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo min(100, $meals_progress ?? 0); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-sm text-gray-400 mt-1">
                                    <span>$<?php echo number_format((float)($donation_stats['meals_amount'] ?? 0), 2); ?></span>
                                    <span>$<?php echo number_format((float)($counters['meals_goal'] ?? 0), 2); ?></span>
                                </div>
                            </div>

                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-white font-medium">مساعدة العائلات</span>
                                    <span class="text-gray-400"><?php echo number_format((float)($families_progress ?? 0), 1); ?>%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo min(100, $families_progress ?? 0); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-sm text-gray-400 mt-1">
                                    <span>$<?php echo number_format((float)($donation_stats['families_amount'] ?? 0), 2); ?></span>
                                    <span>$<?php echo number_format((float)($counters['families_goal'] ?? 0), 2); ?></span>
                                </div>
                            </div>

                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-white font-medium">المأوى الآمن</span>
                                    <span class="text-gray-400"><?php echo number_format((float)($children_progress ?? 0), 1); ?>%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo min(100, $children_progress ?? 0); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-sm text-gray-400 mt-1">
                                    <span>$<?php echo number_format((float)($donation_stats['shelter_amount'] ?? 0), 2); ?></span>
                                    <span>$<?php echo number_format((float)($counters['children_goal'] ?? 0), 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Donation Statistics -->
                    <div class="table-container rounded-xl p-6">
                        <h2 class="text-xl font-bold text-white mb-6">إحصائيات التبرعات الفعلية</h2>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-3 bg-slate-800 rounded-lg">
                                <span class="text-gray-300">إجمالي التبرعات</span>
                                <span class="text-white font-bold"><?php echo number_format((int)($donation_stats['total_donations'] ?? 0)); ?></span>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-slate-800 rounded-lg">
                                <span class="text-gray-300">المتبرعين الفريدين</span>
                                <span class="text-white font-bold"><?php echo number_format((int)($donation_stats['unique_donors'] ?? 0)); ?></span>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-slate-800 rounded-lg">
                                <span class="text-gray-300">المبلغ المؤكد</span>
                                <span class="text-green-400 font-bold">$<?php echo number_format((float)($donation_stats['confirmed_amount'] ?? 0), 2); ?></span>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-slate-800 rounded-lg">
                                <span class="text-gray-300">متوسط التبرع</span>
                                <span class="text-blue-400 font-bold">
                                    $<?php
                                    $totalDonations = (int)($donation_stats['total_donations'] ?? 0);
                                    $confirmedAmount = (float)($donation_stats['confirmed_amount'] ?? 0);
                                    echo $totalDonations > 0 ? number_format($confirmedAmount / $totalDonations, 2) : '0.00';
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Update Form -->
                <div class="table-container rounded-xl p-6">
                    <h2 class="text-xl font-bold text-white mb-6">تحديث العدادات والأهداف</h2>
                    
                    <form method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <input type="hidden" name="action" value="update_counters">
                        
                        <!-- Counters -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-bold text-red-400">العدادات المعروضة</h3>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">عدد الوجبات المقدمة</label>
                                <input type="number" name="meals_count" value="<?php echo (int)($counters['meals_count'] ?? 0); ?>" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>

                            <div>
                                <label class="block text-gray-300 mb-2">عدد العائلات المساعدة</label>
                                <input type="number" name="families_count" value="<?php echo (int)($counters['families_count'] ?? 0); ?>" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>

                            <div>
                                <label class="block text-gray-300 mb-2">عدد الأطفال المساعدين</label>
                                <input type="number" name="children_count" value="<?php echo (int)($counters['children_count'] ?? 0); ?>" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>

                            <div>
                                <label class="block text-gray-300 mb-2">عدد المتبرعين</label>
                                <input type="number" name="donors_count" value="<?php echo (int)($counters['donors_count'] ?? 0); ?>" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>
                        </div>
                        
                        <!-- Goals -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-bold text-red-400">الأهداف المالية (USDT)</h3>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">هدف الوجبات الساخنة</label>
                                <input type="number" name="meals_goal" value="<?php echo (float)($counters['meals_goal'] ?? 0); ?>" step="0.01" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>

                            <div>
                                <label class="block text-gray-300 mb-2">هدف مساعدة العائلات</label>
                                <input type="number" name="families_goal" value="<?php echo (float)($counters['families_goal'] ?? 0); ?>" step="0.01" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>

                            <div>
                                <label class="block text-gray-300 mb-2">هدف المأوى الآمن</label>
                                <input type="number" name="children_goal" value="<?php echo (float)($counters['children_goal'] ?? 0); ?>" step="0.01" class="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-red-500" min="0" required>
                            </div>
                            
                            <div class="pt-4">
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg">
                                    <i class="fas fa-save mr-2"></i>حفظ التحديثات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
