/**
 * CSS لدعم اللغات المتعددة والاتجاهات المختلفة
 * Multi-language and Direction Support CSS
 */

/* إعدادات عامة للاتجاهات */
[dir="rtl"] {
    text-align: right;
    direction: rtl;
}

[dir="ltr"] {
    text-align: left;
    direction: ltr;
}

/* إعدادات إضافية للعربية */
html[lang="ar"] {
    direction: rtl;
}

html[lang="ar"] body {
    direction: rtl;
    text-align: right;
}

/* إعدادات للغات LTR */
html[lang="en"],
html[lang="ru"],
html[lang="de"],
html[lang="sv"],
html[lang="zh"],
html[lang="no"],
html[lang="pt"],
html[lang="tr"],
html[lang="es"],
html[lang="id"],
html[lang="ja"],
html[lang="ms"] {
    direction: ltr;
}

html[lang="en"] body,
html[lang="ru"] body,
html[lang="de"] body,
html[lang="sv"] body,
html[lang="zh"] body,
html[lang="no"] body,
html[lang="pt"] body,
html[lang="tr"] body,
html[lang="es"] body,
html[lang="id"] body,
html[lang="ja"] body,
html[lang="ms"] body {
    direction: ltr;
    text-align: left;
}

/* تخصيص الخطوط حسب اللغة */
html[lang="ar"] {
    font-family: 'Tajawal', sans-serif;
}

html[lang="en"] {
    font-family: 'Inter', sans-serif;
}

html[lang="zh"] {
    font-family: 'Noto Sans SC', sans-serif;
}

html[lang="ja"] {
    font-family: 'Noto Sans JP', sans-serif;
}

html[lang="ru"],
html[lang="de"],
html[lang="sv"],
html[lang="no"],
html[lang="pt"],
html[lang="tr"],
html[lang="es"],
html[lang="id"],
html[lang="ms"] {
    font-family: 'Inter', sans-serif;
}

/* منتقي اللغة */
.language-selector {
    position: relative;
    z-index: 1000;
}

.language-selector .relative {
    position: relative;
}

.language-selector button {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.language-selector button:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.language-selector .origin-top-right {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

[dir="rtl"] .language-selector .origin-top-right {
    right: auto;
    left: 0;
}

.language-selector a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.language-selector a:last-child {
    border-bottom: none;
}

.language-selector a:hover {
    background-color: #f8f9fa;
}

.language-selector a.bg-gray-100 {
    background-color: #e63946;
    color: white;
}

.language-selector a.bg-gray-100:hover {
    background-color: #c1121f;
}

/* منتقي اللغة في الهيدر */
.language-selector-header {
    position: relative;
    z-index: 1000;
}

.language-selector-header button {
    background: #374151;
    border: 1px solid #4b5563;
    color: white;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.language-selector-header button:hover {
    background: #4b5563;
    border-color: #e63946;
    box-shadow: 0 4px 12px rgba(230, 57, 70, 0.2);
}

.language-selector-header button:focus {
    outline: none;
    ring: 2px;
    ring-color: #e63946;
}

.language-selector-header .absolute {
    position: absolute;
    top: 100%;
    margin-top: 8px;
    min-width: 256px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    z-index: 1001;
}

.language-selector-header a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.language-selector-header a:last-child {
    border-bottom: none;
}

.language-selector-header a:hover {
    background-color: #f9fafb;
}

.language-selector-header a.bg-red-50 {
    background-color: #fef2f2;
    color: #dc2626;
}

.language-selector-header a.bg-red-50:hover {
    background-color: #fee2e2;
}

/* منتقي اللغة للموبايل */
.language-selector-mobile {
    position: relative;
}

.language-selector-mobile button {
    background: #374151;
    border: 1px solid #4b5563;
    color: white;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.language-selector-mobile button:hover {
    background: #4b5563;
    border-color: #e63946;
}

.language-selector-mobile .mt-2 {
    margin-top: 8px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 8px;
    overflow: hidden;
}

.language-selector-mobile a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    transition: all 0.2s ease;
    color: #d1d5db;
}

.language-selector-mobile a:hover {
    background-color: #4b5563;
}

.language-selector-mobile a.bg-red-900 {
    background-color: #7f1d1d;
    color: #fecaca;
}

.language-selector-mobile a.bg-red-900:hover {
    background-color: #991b1b;
}

/* تخصيص الأزرار حسب الاتجاه */
[dir="rtl"] .btn-icon-left {
    margin-left: 8px;
    margin-right: 0;
}

[dir="ltr"] .btn-icon-left {
    margin-right: 8px;
    margin-left: 0;
}

[dir="rtl"] .btn-icon-right {
    margin-right: 8px;
    margin-left: 0;
}

[dir="ltr"] .btn-icon-right {
    margin-left: 8px;
    margin-right: 0;
}

/* تخصيص التنقل */
[dir="rtl"] .nav-spacing {
    margin-right: 24px;
}

[dir="ltr"] .nav-spacing {
    margin-left: 24px;
}

/* تخصيص النصوص */
[dir="rtl"] .text-spacing {
    padding-right: 16px;
}

[dir="ltr"] .text-spacing {
    padding-left: 16px;
}

/* تخصيص الأيقونات */
[dir="rtl"] .icon-spacing {
    margin-left: 8px;
    margin-right: 0;
}

[dir="ltr"] .icon-spacing {
    margin-right: 8px;
    margin-left: 0;
}

/* تخصيص النماذج */
[dir="rtl"] .form-label {
    text-align: right;
}

[dir="ltr"] .form-label {
    text-align: left;
}

/* تخصيص الجداول */
[dir="rtl"] table {
    direction: rtl;
}

[dir="ltr"] table {
    direction: ltr;
}

/* تخصيص القوائم */
[dir="rtl"] ul, [dir="rtl"] ol {
    padding-right: 20px;
    padding-left: 0;
}

[dir="ltr"] ul, [dir="ltr"] ol {
    padding-left: 20px;
    padding-right: 0;
}

/* تخصيص الصور والوسائط */
[dir="rtl"] .media-float {
    float: right;
    margin-left: 16px;
    margin-right: 0;
}

[dir="ltr"] .media-float {
    float: left;
    margin-right: 16px;
    margin-left: 0;
}

/* تخصيص الحدود */
[dir="rtl"] .border-start {
    border-right: 1px solid #e5e7eb;
    border-left: none;
}

[dir="ltr"] .border-start {
    border-left: 1px solid #e5e7eb;
    border-right: none;
}

[dir="rtl"] .border-end {
    border-left: 1px solid #e5e7eb;
    border-right: none;
}

[dir="ltr"] .border-end {
    border-right: 1px solid #e5e7eb;
    border-left: none;
}

/* تخصيص الظلال */
[dir="rtl"] .shadow-start {
    box-shadow: 4px 0 6px -1px rgba(0, 0, 0, 0.1);
}

[dir="ltr"] .shadow-start {
    box-shadow: -4px 0 6px -1px rgba(0, 0, 0, 0.1);
}

/* تخصيص التحولات */
[dir="rtl"] .transform-x {
    transform: translateX(8px);
}

[dir="ltr"] .transform-x {
    transform: translateX(-8px);
}

/* تخصيص الرسوم المتحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

[dir="rtl"] .slide-in {
    animation: slideInRight 0.5s ease-out;
}

[dir="ltr"] .slide-in {
    animation: slideInLeft 0.5s ease-out;
}

/* تخصيص الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    z-index: 9999;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

[dir="rtl"] .notification {
    left: 20px;
    transform: translateX(-400px);
}

[dir="ltr"] .notification {
    right: 20px;
    transform: translateX(400px);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* تخصيص النوافذ المنبثقة */
.modal-content {
    direction: inherit;
}

[dir="rtl"] .modal-close {
    left: 15px;
    right: auto;
}

[dir="ltr"] .modal-close {
    right: 15px;
    left: auto;
}

/* تخصيص أشرطة التقدم */
.progress-bar {
    direction: ltr; /* دائماً من اليسار لليمين */
}

/* تخصيص العدادات */
.counter-box {
    text-align: center;
}

/* تخصيص البطاقات */
.card {
    direction: inherit;
}

[dir="rtl"] .card-icon {
    margin-left: 12px;
    margin-right: 0;
}

[dir="ltr"] .card-icon {
    margin-right: 12px;
    margin-left: 0;
}

/* تخصيص التذييل */
[dir="rtl"] .footer-links {
    text-align: right;
}

[dir="ltr"] .footer-links {
    text-align: left;
}

/* تخصيص الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-selector {
        position: fixed;
        top: 10px;
        z-index: 1001;
    }
    
    [dir="rtl"] .language-selector {
        left: 10px;
        right: auto;
    }
    
    [dir="ltr"] .language-selector {
        right: 10px;
        left: auto;
    }
    
    .language-selector button {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .language-selector .origin-top-right {
        min-width: 180px;
    }
}

/* تحسينات إضافية للطباعة */
@media print {
    .language-selector {
        display: none;
    }
    
    .notification {
        display: none;
    }
}
