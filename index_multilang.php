<?php
/**
 * الصفحة الرئيسية متعددة اللغات لموقع يداً بيد لأجل غزة
 */

// تعريف ثابت للأمان
define('CHARITY_GAZA', true);

// تضمين ملفات النظام
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// بدء الجلسة
session_start();

// إنشاء اتصال قاعدة البيانات
$db = new Database();
$conn = $db->getConnection();

// جلب الشهادات
$testimonials = [];
try {
    $stmt = $conn->prepare("SELECT name, role, message FROM testimonials WHERE status = 'approved' ORDER BY created_at DESC LIMIT 6");
    $stmt->execute();
    $testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching testimonials: " . $e->getMessage());
}

// جلب إحصائيات التبرعات
$stats = [
    'total_donations' => 0,
    'total_amount' => 0,
    'total_donors' => 0,
    'verified_donations' => 0
];

try {
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_donations,
            SUM(amount) as total_amount,
            COUNT(DISTINCT donor_email) as total_donors,
            COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_donations
        FROM donations
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $stats = $result;
    }
} catch (PDOException $e) {
    error_log("Error fetching stats: " . $e->getMessage());
}

// جلب إحصائيات إضافية للعرض
$display_stats = [
    'meals' => max(1250, $stats['verified_donations'] * 3),
    'families' => max(420, $stats['total_donors']),
    'children' => max(89, intval($stats['verified_donations'] * 0.7)),
    'donors' => max(156, $stats['total_donors'])
];

// معلومات الصفحة
$page_title = __('nav_home');
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection();
$is_rtl = isRTL();

// تضمين الهيدر
include 'includes/header.php';
?>

    <!-- Header Navigation -->
    <header class="bg-black shadow-md border-b border-red-900">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <div class="h-12 w-12 rounded-full border border-red-900 bg-red-600 flex items-center justify-center">
                    <i class="fas fa-heart text-white text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-red-500 <?php echo $is_rtl ? 'mr-3' : 'ml-3'; ?>"><?php echo __('site_name'); ?></h1>
            </div>
            <nav class="hidden md:block">
                <ul class="flex space-x-6 <?php echo $is_rtl ? 'space-x-reverse' : ''; ?>">
                    <li><a href="index.php" class="text-red-500 font-medium hover:text-red-300"><?php echo __('nav_home'); ?></a></li>
                    <li><a href="charity_kitchen.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_kitchen'); ?></a></li>
                    <li><a href="tragedy.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_tragedy'); ?></a></li>
                    <li><a href="donate.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_donate'); ?></a></li>
                    <li><a href="testimonials.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_testimonials'); ?></a></li>
                    <li><a href="contact.php" class="text-gray-300 hover:text-red-500"><?php echo __('nav_contact'); ?></a></li>
                </ul>
            </nav>
            <div class="md:hidden">
                <button id="mobile-menu-btn" class="text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-gray-900 border-t border-red-900">
            <ul class="py-4">
                <li><a href="index.php" class="block px-4 py-2 text-red-500 hover:bg-gray-800"><?php echo __('nav_home'); ?></a></li>
                <li><a href="charity_kitchen.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-800"><?php echo __('nav_kitchen'); ?></a></li>
                <li><a href="tragedy.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-800"><?php echo __('nav_tragedy'); ?></a></li>
                <li><a href="donate.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-800"><?php echo __('nav_donate'); ?></a></li>
                <li><a href="testimonials.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-800"><?php echo __('nav_testimonials'); ?></a></li>
                <li><a href="contact.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-800"><?php echo __('nav_contact'); ?></a></li>
            </ul>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-bg text-white py-20 md:py-32">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-heartbeat crying-icon text-5xl"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6"><?php echo __('hero_title'); ?></h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"><?php echo __('hero_description'); ?></p>
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="#donate" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full text-lg donate-btn pulse">
                    <?php echo __('donate_now'); ?> <i class="fas fa-hand-holding-heart <?php echo $is_rtl ? 'ml-2' : 'mr-2'; ?>"></i>
                </a>
                <a href="#about" class="border-2 border-white text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-full text-lg transition-all">
                    <?php echo __('learn_more'); ?>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-black text-white py-12 border-t border-b border-red-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('stats_title'); ?></h2>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="mealsCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_meals'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="familiesCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_families'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="sheltersCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_shelters'); ?></div>
                </div>
                <div class="counter-box p-6 rounded-lg">
                    <div class="text-4xl font-bold mb-2 text-red-500" id="medicalCounter">0</div>
                    <div class="text-lg"><?php echo __('stats_medical'); ?></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('about_title'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('about_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bullseye text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_mission'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_mission_desc'); ?></p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-eye text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_vision'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_vision_desc'); ?></p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-heart text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4"><?php echo __('about_values'); ?></h3>
                    <p class="text-gray-300"><?php echo __('about_values_desc'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Donation Packages Section -->
    <section id="donate" class="py-20 bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-red-500 mb-4"><?php echo __('donation_packages'); ?></h2>
                <p class="text-gray-300 max-w-2xl mx-auto"><?php echo __('hero_description'); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- وجبة ساخنة -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-utensils text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_meal'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_meal_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$5</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <button class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg donate-btn" data-package="meal" data-amount="5">
                        <span class="btn-text"><?php echo __('donate_now'); ?></span>
                        <span class="btn-loading hidden">
                            <span class="loading"></span> <?php echo __('processing'); ?>
                        </span>
                    </button>
                </div>

                <!-- مساعدة عائلة -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-home text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_family_aid'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_family_aid_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$50</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <button class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg donate-btn" data-package="family_aid" data-amount="50">
                        <span class="btn-text"><?php echo __('donate_now'); ?></span>
                        <span class="btn-loading hidden">
                            <span class="loading"></span> <?php echo __('processing'); ?>
                        </span>
                    </button>
                </div>

                <!-- مأوى آمن -->
                <div class="donation-card p-6 rounded-lg bg-gray-700 border border-red-900">
                    <div class="text-center mb-6">
                        <i class="fas fa-shield-alt text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo __('package_shelter'); ?></h3>
                        <p class="text-gray-400"><?php echo __('package_shelter_desc'); ?></p>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-red-500">$100</span>
                        <span class="text-gray-400"> <?php echo __('currency'); ?></span>
                    </div>
                    <button class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg donate-btn" data-package="shelter" data-amount="100">
                        <span class="btn-text"><?php echo __('donate_now'); ?></span>
                        <span class="btn-loading hidden">
                            <span class="loading"></span> <?php echo __('processing'); ?>
                        </span>
                    </button>
                </div>
            </div>

            <!-- Wallet Information -->
            <div class="bg-gray-700 p-8 rounded-xl shadow-md max-w-3xl mx-auto border border-red-900">
                <h3 class="text-xl font-bold text-white mb-6 text-center"><?php echo __('wallet_info'); ?></h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-300 mb-2"><?php echo __('wallet_address'); ?>:</label>
                        <div class="flex items-center">
                            <input type="text" value="<?php echo TRON_WALLET_ADDRESS; ?>" readonly class="flex-grow p-3 bg-gray-800 text-white rounded-l-lg border border-gray-600">
                            <button onclick="copyToClipboard('<?php echo TRON_WALLET_ADDRESS; ?>')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-r-lg">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-center">
                        <p class="text-gray-400 text-sm">
                            <i class="fas fa-info-circle <?php echo $is_rtl ? 'ml-1' : 'mr-1'; ?>"></i>
                            <?php echo __('payment_instructions'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>
