<?php
/**
 * إنشاء حساب المشرف
 */

echo "<h1>إنشاء حساب المشرف</h1>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=charity_gaza;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ تم الاتصال بقاعدة البيانات</p>";
    
    // التحقق من وجود جدول admin_users
    $tables = $pdo->query("SHOW TABLES LIKE 'admin_users'")->fetchAll();
    
    if (empty($tables)) {
        // إنشاء جدول admin_users
        $sql = "CREATE TABLE admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            role ENUM('admin', 'moderator') DEFAULT 'admin',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        echo "<p>✅ تم إنشاء جدول admin_users</p>";
    } else {
        echo "<p>✅ جدول admin_users موجود</p>";
    }
    
    // حذف المستخدم القديم إن وجد
    $pdo->exec("DELETE FROM admin_users WHERE username = 'admin'");
    
    // إنشاء كلمة مرور مشفرة
    $password = 'admin123';
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    
    // إدراج المستخدم الجديد
    $sql = "INSERT INTO admin_users (username, password_hash, full_name, email, role, is_active) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        'admin',
        $password_hash,
        'مدير النظام',
        '<EMAIL>',
        'admin',
        1
    ]);
    
    if ($result) {
        echo "<div style='background: green; color: white; padding: 10px; margin: 10px 0;'>
                <h2>✅ تم إنشاء حساب المشرف بنجاح!</h2>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
                <p><a href='admin/login.php' style='color: yellow;'>اذهب لتسجيل الدخول</a></p>
              </div>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء الحساب</p>";
    }
    
    // عرض المستخدمين الموجودين
    $users = $pdo->query("SELECT username, full_name, email, role, is_active FROM admin_users")->fetchAll();
    
    if ($users) {
        echo "<h3>المستخدمين الموجودين:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد</th><th>الدور</th><th>نشط</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "<p>قاعدة البيانات غير موجودة. <a href='simple_setup.php'>شغّل ملف الإعداد أولاً</a></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='simple_setup.php'>إعداد قاعدة البيانات</a></li>";
echo "<li><a href='admin/login.php'>تسجيل دخول الإدارة</a></li>";
echo "<li><a href='index.php'>الموقع الرئيسي</a></li>";
echo "</ul>";
?>
