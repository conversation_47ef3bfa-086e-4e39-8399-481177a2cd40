/**
 * ملف JavaScript الرئيسي لموقع يداً بيد لأجل غزة
 */

// متغيرات عامة
let countersAnimated = false;
let donationModal = null;

// أنواع التبرعات
const donationPackages = {
    'meal': { name: 'وجبة ساخنة', amount: 5, description: 'وجبة ساخنة لعائلة مكونة من 5 أفراد' },
    'family_aid': { name: 'مساعدة عائلة', amount: 50, description: 'مساعدة شهرية لعائلة محتاجة' },
    'shelter': { name: 'مأوى آمن', amount: 100, description: 'توفير مأوى آمن لعائلة مشردة' },
    'medical': { name: 'مساعدة طبية', amount: 75, description: 'توفير الأدوية والعلاج الطبي' },
    'blanket': { name: 'بطانية شتوية', amount: 20, description: 'بطانية شتوية لحماية من البرد' },
    'clothes': { name: 'ملابس شتوية', amount: 30, description: 'كيس ملابس شتوية لطفل' }
};

/**
 * تهيئة العدادات المتحركة
 */
function initializeCounters() {
    // جلب البيانات من الخادم
    fetch('index.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_counters&csrf_token=' + CSRF_TOKEN
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث العدادات عند التمرير
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !countersAnimated) {
                        animateCounters(data.data);
                        countersAnimated = true;
                    }
                });
            });
            
            const statsSection = document.querySelector('#mealsCounter').closest('section');
            if (statsSection) {
                observer.observe(statsSection);
            }
        }
    })
    .catch(error => {
        console.error('Error fetching counters:', error);
        // استخدام قيم افتراضية في حالة الخطأ
        const defaultData = {
            meals: 15420,
            families: 8750,
            children: 12300,
            donors: 2840
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !countersAnimated) {
                    animateCounters(defaultData);
                    countersAnimated = true;
                }
            });
        });
        
        const statsSection = document.querySelector('#mealsCounter').closest('section');
        if (statsSection) {
            observer.observe(statsSection);
        }
    });
}

/**
 * تحريك العدادات
 */
function animateCounters(data) {
    const counters = [
        { element: document.getElementById('mealsCounter'), target: data.meals },
        { element: document.getElementById('familiesCounter'), target: data.families },
        { element: document.getElementById('childrenCounter'), target: data.children },
        { element: document.getElementById('donorsCounter'), target: data.donors }
    ];
    
    counters.forEach(counter => {
        if (counter.element) {
            animateCounter(counter.element, counter.target);
        }
    });
}

/**
 * تحريك عداد واحد
 */
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const duration = 2000; // 2 ثانية
    const stepTime = duration / 100;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString('ar-EG');
    }, stepTime);
}

/**
 * تهيئة أزرار التبرع
 */
function initializeDonationButtons() {
    donationModal = document.getElementById('donationModal');
    
    // إضافة مستمعي الأحداث لأزرار التبرع
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('donate-btn') || e.target.closest('.donate-btn')) {
            const button = e.target.classList.contains('donate-btn') ? e.target : e.target.closest('.donate-btn');

            // إذا كان الزر يحتوي على href، دعه يعمل بشكل طبيعي
            if (button.tagName === 'A' && button.getAttribute('href')) {
                return; // لا تمنع السلوك الافتراضي
            }

            // فقط للأزرار التي لا تحتوي على href
            e.preventDefault();

            const packageType = button.getAttribute('data-package') || 'meal';
            const customAmount = button.getAttribute('data-amount');

            openDonationModal(packageType, customAmount);
        }
    });
    
    // إغلاق النافذة المنبثقة
    const closeBtn = donationModal.querySelector('.close');
    if (closeBtn) {
        closeBtn.onclick = closeModal;
    }
    
    // إغلاق النافذة عند النقر خارجها
    window.onclick = function(event) {
        if (event.target === donationModal) {
            closeModal();
        }
    };
    
    // معالجة نموذج التبرع
    const donationForm = document.getElementById('donationForm');
    if (donationForm) {
        donationForm.addEventListener('submit', handleDonationSubmit);
    }
}

/**
 * فتح نافذة التبرع
 */
function openDonationModal(packageType = 'meal', customAmount = null) {
    const packageInfo = donationPackages[packageType] || donationPackages['meal'];
    
    // تحديث معلومات التبرع
    document.getElementById('donationPackage').value = packageInfo.name;
    document.getElementById('donationAmount').value = customAmount || packageInfo.amount;
    
    // إظهار النافذة
    donationModal.style.display = 'block';
    
    // التركيز على حقل الاسم
    setTimeout(() => {
        document.getElementById('donorName').focus();
    }, 100);
}

/**
 * إغلاق نافذة التبرع
 */
function closeModal() {
    donationModal.style.display = 'none';
    
    // إعادة تعيين النموذج
    document.getElementById('donationForm').reset();
    
    // إخفاء حالة التحميل
    const submitBtn = document.querySelector('#donationForm button[type="submit"]');
    resetButtonState(submitBtn);
}

/**
 * معالجة إرسال نموذج التبرع
 */
function handleDonationSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);
    
    // التحقق من صحة البيانات
    if (!validateDonationForm(formData)) {
        return;
    }
    
    // تغيير حالة الزر
    setButtonLoading(submitBtn, true);
    
    // إرسال البيانات
    fetch('index.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        setButtonLoading(submitBtn, false);
        
        if (data.success) {
            showNotification(data.message, 'success');
            closeModal();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        setButtonLoading(submitBtn, false);
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إرسال التبرع. يرجى المحاولة مرة أخرى.', 'error');
    });
}

/**
 * التحقق من صحة نموذج التبرع
 */
function validateDonationForm(formData) {
    const name = formData.get('name');
    const email = formData.get('email');
    const amount = formData.get('amount');

    if (!name || name.trim() === '') {
        showNotification('الاسم مطلوب', 'error');
        return false;
    }

    if (!email || email.trim() === '' || !isValidEmail(email.trim())) {
        showNotification('البريد الإلكتروني غير صحيح', 'error');
        return false;
    }

    const amountNum = parseFloat(amount);
    if (!amount || isNaN(amountNum) || amountNum < 1 || amountNum > 100000) {
        showNotification('مبلغ التبرع يجب أن يكون بين 1 و 100000 USDT', 'error');
        return false;
    }

    return true;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تهيئة القائمة المحمولة
 */
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
        
        // إغلاق القائمة عند النقر على رابط
        mobileMenu.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                mobileMenu.classList.add('hidden');
            }
        });
    }
}

/**
 * تهيئة نموذج التواصل
 */
function initializeContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(contactForm);
            formData.append('action', 'submit_contact');
            formData.append('csrf_token', CSRF_TOKEN);
            
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            setButtonLoading(submitBtn, true);
            
            fetch('index.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                setButtonLoading(submitBtn, false);
                
                if (data.success) {
                    showNotification(data.message, 'success');
                    contactForm.reset();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                setButtonLoading(submitBtn, false);
                console.error('Error:', error);
                showNotification('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.', 'error');
            });
        });
    }
}

/**
 * تغيير حالة الزر إلى التحميل
 */
function setButtonLoading(button, loading) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (loading) {
        button.disabled = true;
        if (btnText) btnText.classList.add('hidden');
        if (btnLoading) btnLoading.classList.remove('hidden');
    } else {
        button.disabled = false;
        if (btnText) btnText.classList.remove('hidden');
        if (btnLoading) btnLoading.classList.add('hidden');
    }
}

/**
 * إعادة تعيين حالة الزر
 */
function resetButtonState(button) {
    setButtonLoading(button, false);
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
        ${message}
        <button onclick="this.parentElement.remove()" class="float-left ml-2">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    container.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // إخفاء الإشعار تلقائياً
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('تم نسخ عنوان المحفظة بنجاح', 'success');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showNotification('فشل في نسخ عنوان المحفظة', 'error');
    });
}

/**
 * التمرير السلس للأقسام
 */
document.addEventListener('DOMContentLoaded', function() {
    // إضافة التمرير السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

/**
 * تحديث أشرطة التقدم
 */
function updateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-fill');
    
    progressBars.forEach(bar => {
        const percentage = bar.getAttribute('data-percentage') || 0;
        bar.style.width = percentage + '%';
    });
}

// تهيئة أشرطة التقدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateProgressBars, 1000);
});
